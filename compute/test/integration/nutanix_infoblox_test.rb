#!/usr/bin/env ruby

# Test script for Nutanix-Infoblox integration
# This script validates the InfobloxClient functionality

require 'json'
require_relative '../../libraries/infoblox_client'
require_relative '../../libraries/nutanix_infoblox_helper'

class NutanixInfobloxTest
  include NutanixInfobloxHelper
  
  def initialize
    @test_config = {
      'username' => ENV['INFOBLOX_USERNAME'] || 'test_user',
      'password' => ENV['INFOBLOX_PASSWORD'] || 'test_password',
      'grid_servers' => ENV['INFOBLOX_SERVERS']&.split(',') || ['infoblox-test.example.com'],
      'infoblox_url' => ENV['INFOBLOX_URL'] || 'https://infoblox-test.example.com',
      'wapi_version' => ENV['INFOBLOX_WAPI_VERSION'] || 'v2.12'
    }
    
    @test_vm = {
      hostname: 'test-vm-001.example.com',
      ip: '*************',
      mac: '00:50:56:12:34:56'
    }
  end
  
  def run_tests
    puts "Starting Nutanix-Infoblox integration tests..."
    
    test_infoblox_client_initialization
    test_mac_address_generation
    test_network_format_conversion
    test_vm_info_extraction
    
    if ENV['INFOBLOX_LIVE_TEST'] == 'true'
      puts "\nRunning live Infoblox tests (requires valid credentials)..."
      test_live_infoblox_operations
    else
      puts "\nSkipping live Infoblox tests (set INFOBLOX_LIVE_TEST=true to enable)"
    end
    
    puts "\nAll tests completed!"
  end
  
  private
  
  def test_infoblox_client_initialization
    puts "\n--- Testing InfobloxClient initialization ---"
    
    begin
      client = InfobloxClient.new(
        username: @test_config['username'],
        password: @test_config['password'],
        grid_servers: @test_config['grid_servers'],
        infoblox_url: @test_config['infoblox_url'],
        wapi_version: @test_config['wapi_version']
      )
      
      assert_equal(@test_config['username'], client.username, "Username should match")
      assert_equal(@test_config['grid_servers'], client.grid_servers, "Grid servers should match")
      assert_equal(@test_config['wapi_version'], client.wapi_version, "WAPI version should match")
      
      puts "✓ InfobloxClient initialization test passed"
    rescue => e
      puts "✗ InfobloxClient initialization test failed: #{e.message}"
    end
  end
  
  def test_mac_address_generation
    puts "\n--- Testing MAC address generation ---"
    
    begin
      instance_id = "test-instance-12345"
      mac = generate_mac_from_instance_id(instance_id)
      
      assert_match(/^[0-9a-f]{2}:[0-9a-f]{2}:[0-9a-f]{2}:[0-9a-f]{2}:[0-9a-f]{2}:[0-9a-f]{2}$/i, mac, "MAC should be valid format")
      
      # Test deterministic generation
      mac2 = generate_mac_from_instance_id(instance_id)
      assert_equal(mac, mac2, "MAC generation should be deterministic")
      
      # Test different instance ID produces different MAC
      mac3 = generate_mac_from_instance_id("different-instance-67890")
      assert_not_equal(mac, mac3, "Different instance IDs should produce different MACs")
      
      puts "✓ MAC address generation test passed"
      puts "  Generated MAC: #{mac}"
    rescue => e
      puts "✗ MAC address generation test failed: #{e.message}"
    end
  end
  
  def test_network_format_conversion
    puts "\n--- Testing network format conversion ---"
    
    begin
      client = InfobloxClient.new(
        username: @test_config['username'],
        password: @test_config['password'],
        grid_servers: @test_config['grid_servers'],
        infoblox_url: @test_config['infoblox_url']
      )
      
      # Test underscore to slash conversion
      network_with_underscore = "***********_24"
      expected_format = "***********/24"
      converted = client.network_to_infoblox_format(network_with_underscore)
      
      assert_equal(expected_format, converted, "Network format conversion should work")
      
      puts "✓ Network format conversion test passed"
      puts "  Converted: #{network_with_underscore} -> #{converted}"
    rescue => e
      puts "✗ Network format conversion test failed: #{e.message}"
    end
  end
  
  def test_vm_info_extraction
    puts "\n--- Testing VM info extraction ---"
    
    begin
      # Mock node structure
      mock_node = {
        server_name: @test_vm[:hostname],
        ip: @test_vm[:ip],
        workorder: {
          rfcCi: {
            ciAttributes: {
              instance_name: @test_vm[:hostname],
              private_ip: @test_vm[:ip],
              instance_id: 'test-instance-12345'
            }
          }
        }
      }
      
      # Mock the node method to return our mock structure
      def mock_node.[]=(key, value)
        # No-op for setting values
      end
      
      vm_info = extract_vm_info_for_dhcp(mock_node)
      
      assert_equal(@test_vm[:hostname], vm_info[:hostname], "Hostname should be extracted")
      assert_equal(@test_vm[:ip], vm_info[:ip], "IP should be extracted")
      assert_not_nil(vm_info[:mac], "MAC should be generated")
      
      puts "✓ VM info extraction test passed"
      puts "  Extracted: #{vm_info}"
    rescue => e
      puts "✗ VM info extraction test failed: #{e.message}"
    end
  end
  
  def test_live_infoblox_operations
    puts "\n--- Testing live Infoblox operations ---"
    
    begin
      client = InfobloxClient.new(
        username: @test_config['username'],
        password: @test_config['password'],
        grid_servers: @test_config['grid_servers'],
        infoblox_url: @test_config['infoblox_url']
      )
      
      # Test grid status
      puts "Testing grid status..."
      status = client.grid_status
      puts "Grid status: #{status}"
      
      # Test network server finding (this will likely fail in test environment)
      puts "Testing network server finding..."
      begin
        server = client.find_network_server("***********/24")
        puts "Found server: #{server}"
      rescue => e
        puts "Expected failure finding test network: #{e.message}"
      end
      
      puts "✓ Live Infoblox operations test completed"
    rescue => e
      puts "✗ Live Infoblox operations test failed: #{e.message}"
      puts "This is expected if credentials are not valid or servers are not reachable"
    end
  end
  
  def assert_equal(expected, actual, message)
    unless expected == actual
      raise "Assertion failed: #{message}. Expected: #{expected}, Actual: #{actual}"
    end
  end
  
  def assert_not_equal(expected, actual, message)
    if expected == actual
      raise "Assertion failed: #{message}. Expected values to be different, but both were: #{expected}"
    end
  end
  
  def assert_match(pattern, string, message)
    unless string.match(pattern)
      raise "Assertion failed: #{message}. Pattern: #{pattern}, String: #{string}"
    end
  end
  
  def assert_not_nil(value, message)
    if value.nil?
      raise "Assertion failed: #{message}. Value was nil"
    end
  end
end

# Run tests if this file is executed directly
if __FILE__ == $0
  test = NutanixInfobloxTest.new
  test.run_tests
end
