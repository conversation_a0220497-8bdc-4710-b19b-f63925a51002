CIRCUIT_PATH     = '/opt/oneops/inductor/circuit-oneops-1'.freeze
COOKBOOKS_PATH   = "#{CIRCUIT_PATH}/components/cookbooks".freeze
AZURE_TESTS_PATH = "#{COOKBOOKS_PATH}/azure/test/integration/add/serverspec".freeze
SHARED_PATH     = '/opt/oneops/inductor/shared/cookbooks/shared/'.freeze

require "#{CIRCUIT_PATH}/components/spec_helper.rb"
Dir.glob("#{SHARED_PATH}/libraries/*.rb").each {|lib| require lib}

$nu = ComputeComponent::NodeUtils.new($node)

case $nu.provider
  when /azure/
    Dir.glob("#{AZURE_TESTS_PATH}/*.rb").each {|tst| require tst}
  when /openstack/
    require "#{COOKBOOKS_PATH}/compute/test/integration/add/serverspec/openstack.rb"
  when /gcp/
    puts "No tests for provider: #{$nu.provider}"
    # require "#{COOKBOOKS_PATH}/compute/test/integration/add/serverspec/gcp.rb"
  else
    puts "No tests for provider: #{$nu.provider}"
end
