Dir.glob("#{COOKBOOKS_PATH}/gcp_base/libraries/*.rb").each {|lib| require lib}
require "#{SHARED_PATH}/libraries/size_manager.rb"

dep = $node[:workorder][:payLoad][:DependsOn]
computeset = dep.detect { |d| d['ciClassName'] =~ /Computeset/i }
mig = GCP::RegionalMIGMgr.new($node)
mig_name = computeset[:ciAttributes]['vmss_name']
mig.mig_name = mig_name
vmids_map = JSON.parse(computeset[:ciAttributes][:vmids_map])
gcp_ord = vmids_map[$node[:workorder][:rfcCi][:ciName].split('-').last]
if gcp_ord.nil?
  exit_with_error("GCP VM ordinal was not found.")
end
gcp_inst_name = "#{mig_name}-#{gcp_ord}"
vm_obj = mig.get_vm(gcp_inst_name)
$size_mgr = ComputeComponent::SizeManager.new($node)
describe "Compute details" do
  it "size should match" do
    expect(vm_obj['machineType'].getname).to be == $size_mgr.calculate_vm_size[:id]
  end
end