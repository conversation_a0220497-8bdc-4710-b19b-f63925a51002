CIRCUIT_PATH     = '/opt/oneops/inductor/circuit-oneops-1'.freeze
COOKBOOKS_PATH   = "#{CIRCUIT_PATH}/components/cookbooks".freeze
AZURE_TESTS_PATH = "#{COOKBOOKS_PATH}/azure/test/integration/update/serverspec".freeze
SHARED_PATH     = '/opt/oneops/inductor/shared/cookbooks/shared/'.freeze

require "#{CIRCUIT_PATH}/components/spec_helper.rb"
require "#{COOKBOOKS_PATH}/azure_base/test/integration/spec_utils"
Dir.glob("#{SHARED_PATH}/libraries/*.rb").each {|lib| require lib}

provider = SpecUtils.new($node).get_provider

case provider
when /openstack/
  require "#{COOKBOOKS_PATH}/compute/test/integration/add/serverspec/openstack.rb"
when /azure/
  Dir.glob("#{AZURE_TESTS_PATH}/*.rb").each {|tst| require tst}
when /gcp/
  puts "No tests for provider: #{provider}"
  # require "#{COOKBOOKS_PATH}/compute/test/integration/add/serverspec/gcp.rb"
else
  puts "No tests for provider: #{provider}"
end
