require "/opt/oneops/inductor/circuit-oneops-1/components/spec_helper.rb"
require 'fog/openstack'

cloud_name = $node['workorder']['cloud']['ciName']
provider = $node['workorder']['services']['compute'][cloud_name]['ciClassName'].gsub("cloud.service.","").downcase.split(".").last

if provider =~ /openstack/i
  compute_service = $node['workorder']['services']['compute'][cloud_name]['ciAttributes']
  rfcCi = $node["workorder"]["rfcCi"]
  nsPathParts = rfcCi["nsPath"].split("/")

  # TO-DO incapsulate server_name into a separate function.
  # Save in a library file and use for both compute recipes and KCI tests
  server_name = $node['workorder']['box']['ciName']+'-'+nsPathParts[3]+'-'+nsPathParts[2]+'-'+nsPathParts[1]+'-'+ rfcCi['ciId'].to_s
  if(server_name.size > 63)
    server_name = server_name.slice(0, 63 - rfcCi['ciId'].to_s.size - 1) +
      '-' + rfcCi['ciId'].to_s
  end

  # connect to openstack client
  conn = Fog::Compute.new(openstack_creds(compute_service))

  # Find your compute
  servers = conn.servers.all(:name => server_name)
  server = servers.first if servers

  describe "Openstack connection" do
    it "should not be nill" do
      expect(conn.nil?).to be == false
    end
  end
  describe "Compute connection", :if => !conn.nil? do
    it "should not exist" do
      expect(server.nil?).to be == true
    end
  end
end

