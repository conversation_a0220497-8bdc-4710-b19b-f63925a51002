
module ComputeComponent
  class ServiceRegistration < NodeUtils
    attr_reader :ip

    def initialize(node)
      super(node)
      @ip = @node[:ip] || @ci[:ciAttributes][:dns_record]
      @consul_env = consul_env
    end

    def register_host
      resource = 'oneops/hosts/create'
      result, code = http_request('Put', resource, payload_host.to_json)
      log_service('REG', result, code, [@consul_env, @ip, 'HOST'].join('/'))
      result
    end

    def deregister_host(host_env)
      env, host = host_env.split('/')
      resource = "oneops/hosts/#{host}/delete"
      result, code = http_request('Put', resource, payload_dereg.to_json, nil, env)
      log_service('UNREG', result, code, host_env + '/HOST')
      result
    end

    def register_service(service)
      env, ip, svc = service.split('/')
      svc_name, svc_port = svc.split(':')
      token = @sm2_token if service == 'sm2-envoy:15090'
      resource = "oneops/services/#{svc_name}/endpoints/create"
      result, code = http_request('Put', resource, payload_service(svc_port).to_json, token, env)
      log_service('REG', result, code, service)
      result
    end

    def deregister_service(service)
      env, ip, svc = service.split('/')
      svc_name, svc_port = svc.split(':')
      token = @sm2_token if service == 'sm2-envoy:15090'
      resource = "oneops/services/#{svc_name}/endpoints/#{ip}/delete"
      result, code = http_request('Put', resource, payload_dereg.to_json, token, env)
      log_service('UNREG', result, code, service)
      result
    end

    # Determines the name of consul environment to be used for API calls
    # Returns consul_env_name (string)
    def consul_env
      env_manifest = @ci[:ciAttributes][:svc_environment]
      if env_manifest && %w(prod nonprod).include?(env_manifest)
        env_manifest
      else
        @profile.downcase.start_with?('prod') ? 'prod' : 'stg'
      end
    end

    def sr_env
      env_manifest = os_attr[:sr_environment]
      if env_manifest && %w(prod nonprod).include?(env_manifest)
        env_manifest
      else
        @profile.downcase.start_with?('prod') ? 'prod' : 'stg'
      end
    end

    def sragent_vars
      _, cloud_tenant, assembly, env, _, plt = @ci[:nsPath].split('/')
      cloud_tenant = @compute_svc_attr[:tenant] unless @provider =~ /azure|gcp/i
      provider_region = @compute_svc_attr[:provider_region] || '' #east|west|central
      sr_datacenter = datacenter_mms(sr_env)

      vars = {
        :SRAGENT_SRDATACENTER => sr_datacenter,
        :SRAGENT_PROVIDER => @provider,
        :SRAGENT_PROVIDER_REGION => provider_region,
        :SRAGENT_ZONE => zone,
        :SRAGENT_DATACENTER => datacenter,
        :SRAGENT_ENVPROFILE => @profile,
        :SRAGENT_PROFILE => production? ? 'prod' : 'stg',
        :SRAGENT_IP => ip_addr,
        :SRAGENT_CLOUD => @cloud_name,
        :SRAGENT_ASSEMBLY => assembly,
        :SRAGENT_PLATFORM => plt,
        :SRAGENT_CLOUD_TENANT => cloud_tenant,
        :SRAGENT_ENVIRONMENT => env
      }
      return vars
    end

    private

    # MMS requires -prod/-stg suffix in the datacenter name
    # Returns datacenter name (string)
    def datacenter_mms(env)
      if datacenter =~ /-stg$|-prod$/
        datacenter
      else
        "#{datacenter}-#{env}"
      end
    end

    # result codes:
    # -1 secrets file is not configured
    # -2 error loading secrets file
    # -3 Either API URL or Auth Token has not been determined
    def load_secrets
      require 'json'
      secrets_file = '/secrets/consul.json'
      unless File.exists?(secrets_file)
        Chef::Log.error("#{secrets_file} secrets file does not exist")
        return -1
      end

      # Load data from secrets file
      begin
        secrets = JSON.parse(File.read(secrets_file))
      rescue => e
        Chef::Log.error('Exception while loading secrets')
        Chef::Log.error(e.message)
        return -2
      end
      return 0, secrets
    end


    def http_request(method, resource, payload = nil, token = nil, env = nil)
      # Load secrets if we haven't done that yet
      rc, secrets = load_secrets
      if rc != 0
        return false, rc
      end

      # Determine api_url and auth_token, fail if nil/empty
      env ||= @consul_env
      url = secrets[env]['url']
      token ||= secrets[env]['authKey']
      secrets = nil

      if url.nil? || url.empty? || token.nil? || token.empty?
        Chef::Log.error('Either API URL or Auth Token has not been determined')
        return false, (-3)
      end

      require 'net/http'
      uri = URI.parse("#{url}/#{resource}")
      https = Net::HTTP.new(uri.host, uri.port)
      https.use_ssl = (uri.scheme == 'https')
      https.verify_mode = OpenSSL::SSL::VERIFY_NONE

      http_method = Net::HTTP.const_get(method)
      request = http_method.new(uri.request_uri)
      request['Content-Type'] = 'application/json'
      request['Datacenter'] = datacenter_mms(env)
      request['X-Auth-Token'] = token
      request.body = payload if payload

      response = nil
      result = false
      token = nil

      begin
        response = https.request(request)
        if response.code == '200'
          Chef::Log.debug("Request to #{uri}: #{response.body}")
          result = true
        elsif resource =~ /\/services\/.+\/delete$/ && response.code == '500' &&
        response.body =~ /Unknown service/
          Chef::Log.warn("Ignoring failure at delete request to #{uri}: #{response.body}")
          result = true
        else
          Chef::Log.error("Failed #{method} request to #{uri}: "\
            "#{response.code} => #{response.body}")
        end
      rescue => e
        Chef::Log.error("Exception while making #{method} request to #{uri}:")
        Chef::Log.error(e.message)
        Chef::Log.error(response) if response
      end
      return result, (response.respond_to?('code')? response.code : 999)
    end

    def payload_host
      _, _, asm, env, _, plt = @ci[:nsPath].split('/')
      {
        'host' => {
          'address' => @ip,
          'name' => @node[:vmhostname],
          'oneops' => {
              'envProfile' => @profile,
              'assembly' => asm,
              'platform' => plt,
              'environment' => env,
              'cloud' => @cloud_name,
              'cloudAvailZone' => (@ci[:ciAttributes][:availability_zone] || '')
          },
        },
        'assemblyTags' => tags_wo(@node, :Assembly)
      }
    end

    def payload_service(port)
      {
        "serviceEndpoint" => {
          'hostAddress' => @ip,
          'attributes'  => { 'port' => port.to_i },
        },
        'assemblyTags' => tags_wo(@node, :Assembly)
      }
    end

    def payload_dereg
      { 'assemblyTags' => tags_wo(@node, :Assembly) }
    end

    def log_service(reg, result, code, service)
      env, ip, svc = service.split('/')
      arr = [result.to_s.downcase == 'true', code, svc, ip, @ci[:nsPath], env]
      puts "***SR_#{reg}:#{arr.join('|')}"
    end
  end
end
