require 'net/http'
require 'json'
require 'uri'
require 'openssl'

class InfobloxClient
  attr_reader :username, :password, :grid_servers, :infoblox_url, :wapi_version

  def initialize(username:, password:, grid_servers:, infoblox_url:, wapi_version: 'v2.12')
    @username = username
    @password = password
    @grid_servers = grid_servers
    @infoblox_url = infoblox_url
    @wapi_version = wapi_version
    puts "[Init] InfobloxClient initialized with WAPI version: #{@wapi_version}"
  end

  # Convert network format to Infoblox style (slash notation)
  def network_to_infoblox_format(network)
    formatted = network.gsub('_', '/')
    puts "[Network] Converted network format: #{network} -> #{formatted}"
    formatted
  end

  # Find the grid server that manages a given network
  def find_network_server(ib_network)
    puts "[FindServer] Looking for grid server managing network: #{ib_network}"
    grid_servers.each do |server|
      url = URI("https://#{server}/wapi/#{wapi_version}/network?network=#{ib_network}")
      begin
        puts "[FindServer] Querying server #{server} for network"
        response = infoblox_get(url)
        if response.is_a?(Array) && !response.empty?
          puts "[FindServer] Server #{server} manages network #{ib_network}"
          return server
        end
      rescue => e
        puts "[FindServer] Failed to query server #{server}: #{e.message}"
      end
    end
    raise 'No Infoblox grid server found with the specified network'
  end

  # Create DHCP reservations in parallel
  def reserve_batch_parallel(vm_list:, server:)
    puts "[ReserveBatch] Starting parallel reservations on #{server}"
    threads = []
    created_any = false
    mutex = Mutex.new

    vm_list.each do |vm|
      threads << Thread.new do
        begin
          result = create_dhcp_reservation(
            server: server,
            hostname: vm[:hostname],
            ip_address: vm[:ip],
            mac: vm[:mac],
            restart: false
          )
          mutex.synchronize { created_any = true } if result
        rescue => e
          puts "[ReserveBatch] Error for #{vm[:hostname]}: #{e.message}"
        end
      end
    end
    threads.each(&:join)

    if created_any
      puts "[ReserveBatch] New reservations created. Checking restart requirement..."
      if restart_required?
        puts "[ReserveBatch] Restart required. Restarting services..."
        restart_service
      else
        puts "[ReserveBatch] No restart required."
      end
    else
      puts "[ReserveBatch] No new reservations created."
    end
  end

  # Create a single DHCP reservation
  def create_dhcp_reservation(server:, hostname:, ip_address:, mac:, restart: false)
    puts "[CreateReservation] Checking existing reservation for #{ip_address} / #{mac}"
    existing = reservation_exists?(server: server, ip_address: ip_address, mac: mac)

    case existing
    when Hash
      puts "[CreateReservation] Matching reservation exists. Skipping."
      return nil
    when :mismatch
      raise "[CreateReservation] Conflict: IP #{ip_address} reserved with different MAC"
    end

    puts "[CreateReservation] Creating reservation for #{hostname} at #{ip_address}"
    url  = URI("https://#{server}/wapi/#{wapi_version}/fixedaddress?_return_fields=ipv4addr,mac&_return_as_object=1")
    body = { name: hostname, ipv4addr: ip_address, mac: mac }
    reservation = infoblox_post(url, body)

    restart_service if restart
    reservation
  end

  # Check if a reservation exists and matches MAC
  def reservation_exists?(server:, ip_address:, mac:)
    query = URI.encode_www_form(ipv4addr: ip_address, _return_as_object: 1)
    url = URI("https://#{server}/wapi/#{wapi_version}/fixedaddress?#{query}")
    puts "[CheckReservation] Querying #{url}"
    resp = infoblox_get(url)
    return nil unless resp.is_a?(Array) && !resp.empty?

    match = resp.find { |r| r['mac'].casecmp(mac).zero? }
    puts match ? "[CheckReservation] Matching reservation found." : "[CheckReservation] Mismatch MAC."
    match || :mismatch
  rescue => e
    puts "[CheckReservation] Error: #{e.message}"
    nil
  end

  # Remove a single DHCP reservation
  def remove_dhcp_reservation(server:, ip_address:, mac: nil, hostname: nil, match_by: :ip)
    puts "[RemoveReservation] Looking up reservation (by #{match_by})"
    query = case match_by
            when :mac then URI.encode_www_form(mac: mac, _return_as_object: 1)
            when :hostname then URI.encode_www_form(name: hostname, _return_as_object: 1)
            else URI.encode_www_form(ipv4addr: ip_address, _return_as_object: 1)
            end
    url = URI("https://#{server}/wapi/#{wapi_version}/fixedaddress?#{query}")
    reservations = infoblox_get(url)
    return false unless reservations.is_a?(Array) && !reservations.empty?

    reservation = reservations.find do |r|
      match_by == :ip || (match_by == :mac && r['mac'].casecmp(mac).zero?) || (match_by == :hostname && r['name'] == hostname)
    end
    return false unless reservation

    ref = reservation['_ref']
    puts "[RemoveReservation] Deleting #{ref}" 
    infoblox_delete(URI("https://#{server}/wapi/#{wapi_version}/#{ref}"))
    reservation
  rescue => e
    puts "[RemoveReservation] Error: #{e.message}"
    false
  end

  # Bulk deletion with summary log & rollback script
  def remove_dhcp_reservations_bulk(vm_list:, server:, match_by: :ip)
    puts "[BulkRemove] Deleting #{vm_list.size} reservations (by #{match_by})"
    threads = []
    mutex = Mutex.new
    deleted = []

    vm_list.each do |vm|
      threads << Thread.new do
        result = remove_dhcp_reservation(
          server: server,
          ip_address: vm[:ip],
          mac: vm[:mac],
          hostname: vm[:hostname],
          match_by: match_by
        )
        mutex.synchronize { deleted << result } if result
      rescue => e
        puts "[BulkRemove] Error for #{vm[:hostname]}: #{e.message}"
      end
    end
    threads.each(&:join)

    if deleted.any?
      puts "[BulkRemove] #{deleted.size} deleted. Writing logs..."
      File.write('deletion-summary.json', JSON.pretty_generate(deleted))
      rollback = deleted.map { |r| { hostname: r['name'], ip: r['ipv4addr'], mac: r['mac'] } }
      File.write('rollback-reservations.json', JSON.pretty_generate(rollback))
    else
      puts "[BulkRemove] No reservations deleted."
    end
  end

  # Fetch grid status and derive restart requirement
  def grid_status
    url = URI("#{infoblox_url}/wapi/#{wapi_version}/grid?_return_fields=service_status,restart_status")
    puts "[GridStatus] GET #{url}"
    result = infoblox_get(url).first
    status = result['restart_status'] || {}
    needed = status['needed_restart'].to_i
    pending = status['pending_restart'].to_i
    puts "[GridStatus] needed_restart=#{needed}, pending_restart=#{pending}"
    result.merge('restart_required' => (needed > 0 || pending > 0))
  end

  # Check if a restart is required based on grid_status
  def restart_required?
    info = grid_status
    info['restart_required']
  end

  # Trigger DHCP service restart on the grid master
  def restart_service(server: server)
    puts "[Restart] Initiating DHCP restart"
    ref = get_grid_ref
    url = URI("https://#{server}/wapi/#{wapi_version}/#{ref}?_function=restartservices")
    body = { 'member_order' => 'SEQUENTIALLY', 'restart_option' => 'FORCE_RESTART', 'service_option' => 'DHCP', 'sequential_delay' => 1 }
    infoblox_post(url, body)
  rescue => e
    puts "[Restart] Error: #{e.message}"
    nil
  end

  private

  def get_grid_ref
    url = URI("#{infoblox_url}/wapi/#{wapi_version}/grid")
    response = infoblox_get(url)
    ref = response.is_a?(Array) ? response.first['_ref'] : response['_ref']
    puts "[GridRef] #{ref}"
    ref
  end

  def infoblox_get(uri)
    puts "[HTTP GET] #{uri}"
    req = Net::HTTP::Get.new(uri)
    req.basic_auth(username, password)
    req['Content-Type'] = 'application/json'
    res = send_request(uri, req)
    body = JSON.parse(res.body)
    body = body['result'] if body.is_a?(Hash) && body.key?('result')
    body
  end

  def infoblox_post(uri, body)
    puts "[HTTP POST] #{uri} -> #{body}"
    req = Net::HTTP::Post.new(uri)
    req.basic_auth(username, password)
    req['Content-Type'] = 'application/json'
    req.body = body.to_json
    res = send_request(uri, req)
    JSON.parse(res.body)
  end

  def infoblox_delete(uri)
    puts "[HTTP DELETE] #{uri}"
    req = Net::HTTP::Delete.new(uri)
    req.basic_auth(username, password)
    req['Content-Type'] = 'application/json'
    res = send_request(uri, req)
    res.body.empty? ? true : JSON.parse(res.body)
  end

  def send_request(uri, req)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    res = http.request(req)
    puts "[HTTP Response] #{res.code} #{res.message}"
    if res.is_a?(Net::HTTPRedirection)
      return send_request(URI(res['location']), req.class.new(URI(res['location'])))
    end
    res
  end
end
