require_relative 'infoblox_client'

module NutanixInfobloxHelper
  
  # Initialize Infoblox client from compute service configuration
  def initialize_infoblox_client(compute_service)
    infoblox_config = JSON.parse(compute_service[:infoblox_config] || '{}')
    
    unless infoblox_config.empty?
      InfobloxClient.new(
        username: infoblox_config['username'],
        password: infoblox_config['password'],
        grid_servers: infoblox_config['grid_servers'],
        infoblox_url: infoblox_config['infoblox_url'],
        wapi_version: infoblox_config['wapi_version'] || 'v2.12'
      )
    else
      Chef::Log.warn("No Infoblox configuration found in compute service")
      nil
    end
  end

  # Extract VM information for DHCP reservation
  def extract_vm_info_for_dhcp(node)
    wo = node[:workorder]
    rfcCi = wo[:rfcCi]
    
    # Get VM details
    hostname = node[:server_name] || rfcCi[:ciAttributes][:instance_name]
    ip_address = node[:ip] || rfcCi[:ciAttributes][:private_ip]
    
    # Extract MAC address from VM metadata or generate if needed
    mac_address = extract_mac_address(node)
    
    {
      hostname: hostname,
      ip: ip_address,
      mac: mac_address
    }
  end

  # Extract MAC address from VM or generate one
  def extract_mac_address(node)
    wo = node[:workorder]
    
    # Try to get MAC from VM metadata if available
    if wo[:rfcCi][:ciAttributes][:metadata]
      begin
        metadata = JSON.parse(wo[:rfcCi][:ciAttributes][:metadata])
        return metadata['mac_address'] if metadata['mac_address']
      rescue JSON::ParserError
        Chef::Log.warn("Failed to parse VM metadata for MAC address")
      end
    end
    
    # Try to get MAC from instance_id or generate
    instance_id = wo[:rfcCi][:ciAttributes][:instance_id] || node[:instance_id]
    if instance_id
      # Generate a deterministic MAC based on instance ID
      generate_mac_from_instance_id(instance_id)
    else
      Chef::Log.warn("No instance ID available for MAC generation")
      nil
    end
  end

  # Generate a MAC address from instance ID
  def generate_mac_from_instance_id(instance_id)
    require 'digest'
    
    # Create a hash from the instance ID
    hash = Digest::SHA256.hexdigest(instance_id)
    
    # Take first 12 characters and format as MAC
    mac_hex = hash[0, 12]
    mac_parts = mac_hex.scan(/../)
    
    # Ensure it's a valid unicast MAC (clear multicast bit, set local bit)
    first_octet = mac_parts[0].to_i(16)
    first_octet = (first_octet & 0xFE) | 0x02  # Clear multicast, set local
    mac_parts[0] = sprintf("%02x", first_octet)
    
    mac_parts.join(':')
  end

  # Get network information for Infoblox
  def get_network_info(compute_service)
    # Extract network information from compute service
    subnetwork = JSON.parse(compute_service[:subnetwork] || '[]').first
    if subnetwork
      subnet_parts = subnetwork.split(':')
      {
        subnet_uuid: subnet_parts[0],
        subnet_name: subnet_parts[1],
        network: subnet_parts[1] # Use subnet name as network identifier
      }
    else
      Chef::Log.warn("No subnetwork configuration found")
      nil
    end
  end

  # Create DHCP reservation for Nutanix VM
  def create_dhcp_reservation_for_vm(node, compute_service)
    infoblox_client = initialize_infoblox_client(compute_service)
    return false unless infoblox_client

    vm_info = extract_vm_info_for_dhcp(node)
    return false unless vm_info[:mac]

    network_info = get_network_info(compute_service)
    return false unless network_info

    begin
      # Find the appropriate Infoblox server for this network
      ib_network = infoblox_client.network_to_infoblox_format(network_info[:network])
      server = infoblox_client.find_network_server(ib_network)
      
      # Create the DHCP reservation
      result = infoblox_client.create_dhcp_reservation(
        server: server,
        hostname: vm_info[:hostname],
        ip_address: vm_info[:ip],
        mac: vm_info[:mac],
        restart: true
      )
      
      if result
        Chef::Log.info("Successfully created DHCP reservation for #{vm_info[:hostname]} (#{vm_info[:ip]})")
        return true
      else
        Chef::Log.info("DHCP reservation already exists for #{vm_info[:hostname]} (#{vm_info[:ip]})")
        return true
      end
      
    rescue => e
      Chef::Log.error("Failed to create DHCP reservation: #{e.message}")
      Chef::Log.error(e.backtrace.join("\n"))
      return false
    end
  end

  # Remove DHCP reservation for Nutanix VM
  def remove_dhcp_reservation_for_vm(node, compute_service)
    infoblox_client = initialize_infoblox_client(compute_service)
    return false unless infoblox_client

    vm_info = extract_vm_info_for_dhcp(node)
    network_info = get_network_info(compute_service)
    return false unless network_info

    begin
      # Find the appropriate Infoblox server for this network
      ib_network = infoblox_client.network_to_infoblox_format(network_info[:network])
      server = infoblox_client.find_network_server(ib_network)
      
      # Remove the DHCP reservation (try by IP first, then by MAC if available)
      result = infoblox_client.remove_dhcp_reservation(
        server: server,
        ip_address: vm_info[:ip],
        mac: vm_info[:mac],
        hostname: vm_info[:hostname],
        match_by: :ip
      )
      
      if result
        Chef::Log.info("Successfully removed DHCP reservation for #{vm_info[:hostname]} (#{vm_info[:ip]})")
        return true
      else
        Chef::Log.info("No DHCP reservation found for #{vm_info[:hostname]} (#{vm_info[:ip]})")
        return true
      end
      
    rescue => e
      Chef::Log.error("Failed to remove DHCP reservation: #{e.message}")
      Chef::Log.error(e.backtrace.join("\n"))
      return false
    end
  end

  # Batch create DHCP reservations for multiple VMs
  def create_dhcp_reservations_batch(vm_list, compute_service)
    infoblox_client = initialize_infoblox_client(compute_service)
    return false unless infoblox_client

    network_info = get_network_info(compute_service)
    return false unless network_info

    begin
      # Find the appropriate Infoblox server for this network
      ib_network = infoblox_client.network_to_infoblox_format(network_info[:network])
      server = infoblox_client.find_network_server(ib_network)
      
      # Create reservations in parallel
      infoblox_client.reserve_batch_parallel(vm_list: vm_list, server: server)
      
      Chef::Log.info("Completed batch DHCP reservation creation for #{vm_list.size} VMs")
      return true
      
    rescue => e
      Chef::Log.error("Failed to create batch DHCP reservations: #{e.message}")
      Chef::Log.error(e.backtrace.join("\n"))
      return false
    end
  end

  # Batch remove DHCP reservations for multiple VMs
  def remove_dhcp_reservations_batch(vm_list, compute_service)
    infoblox_client = initialize_infoblox_client(compute_service)
    return false unless infoblox_client

    network_info = get_network_info(compute_service)
    return false unless network_info

    begin
      # Find the appropriate Infoblox server for this network
      ib_network = infoblox_client.network_to_infoblox_format(network_info[:network])
      server = infoblox_client.find_network_server(ib_network)
      
      # Remove reservations in bulk
      infoblox_client.remove_dhcp_reservations_bulk(vm_list: vm_list, server: server, match_by: :ip)
      
      Chef::Log.info("Completed batch DHCP reservation removal for #{vm_list.size} VMs")
      return true
      
    rescue => e
      Chef::Log.error("Failed to remove batch DHCP reservations: #{e.message}")
      Chef::Log.error(e.backtrace.join("\n"))
      return false
    end
  end

  # Check if Infoblox integration is enabled
  def infoblox_enabled?(compute_service)
    infoblox_config = JSON.parse(compute_service[:infoblox_config] || '{}')
    !infoblox_config.empty? && infoblox_config['enabled'] != false
  end
end
