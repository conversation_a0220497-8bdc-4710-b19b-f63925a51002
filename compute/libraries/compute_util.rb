# get enabled network using the openstack compute cloud service
def get_enabled_network(compute_service,attempted_networks)

  has_sdn = true
  enabled_networks = []
  if compute_service.has_key?('enabled_networks')
    enabled_networks = JSON.parse(compute_service['enabled_networks'])
  end

  if enabled_networks.nil? || enabled_networks.empty?
    enabled_networks = [ compute_service['subnet'] ]
  end

  enabled_networks = enabled_networks - attempted_networks

  if enabled_networks.size == 0
    exit_with_error "no ip available in enabled networks for cloud #{node[:workorder][:cloud][:ciName]}. tried: #{attempted_networks} - escalate to openstack team"
  end

  network_name = enabled_networks.sample
  Chef::Log.info("network_name: "+network_name)

  # net_id for specifying network to use via subnet attr
  net_id = ''
  begin
    quantum = Fog::Network.new(openstack_creds(compute_service))

    quantum.networks.each do |net|
      if net.name == network_name
        Chef::Log.info("network_id: "+net.id)
        net_id = net.id
        break
      end
    end
  rescue Exception => e
    Chef::Log.warn("no quantum or neutron networking installed")
    has_sdn = false
  end

  exit_with_error "Your #{node[:workorder][:cloud][:ciName]} cloud is configured to use network: #{compute_service[:subnet]} but is not found." if net_id.empty? && has_sdn

  return network_name, net_id
end

def is_propagate_update
  rfcCi = node.workorder.rfcCi
  if (rfcCi.ciBaseAttributes.nil? || rfcCi.ciBaseAttributes.empty?) && rfcCi.has_key?('hint') && !rfcCi.hint.empty?
    hint = JSON.parse(rfcCi.hint)
    puts "rfc hint >> " + rfcCi.hint.inspect
    if hint.has_key?('propagation') && hint['propagation'] == 'true'
      return true;
    end
  end
  return false
end

def compute_metadata(node)
  pl = node[:workorder][:payLoad]

  mgmt_url = "https://#{node[:mgmt_domain]}"
  mgmt_url = node[:mgmt_url] if (node[:mgmt_url] && !node[:mgmt_url].empty?)

  result =
  {
    'owner' => (pl[:Assembly][0][:ciAttributes][:owner] || 'na'),
    'mgmt_url' => mgmt_url,
    'organization' => pl[:Organization][0][:ciName],
    'assembly' => pl[:Assembly][0][:ciName],
    'environment' => pl[:Environment][0][:ciName],
    'platform' => node[:workorder][:box][:ciName],
    'component' => pl[:RealizedAs][0][:ciId].to_s,
    'instance' => node[:workorder][:rfcCi][:ciId].to_s
  }

  # Add Team Roster and APM ID tags to the metadata
  filter = %w(trproductid apmid)
  tags = tags_wo(node, :Assembly, filter)
  result.merge(tags)
end

def vmss_map(node)
  vmss_map = nil
  dep = node[:workorder][:payLoad][:DependsOn]
  if dep
    vmss_ci = dep.detect do |d|
      %w(Azurescaleset Computeset).include?(d[:ciClassName].split('.').last)
    end
    if vmss_ci
      require 'json'
      vmss_map = JSON.parse(vmss_ci[:ciAttributes][:vmids_map])
    end
  end
  (vmss_map && !vmss_map.empty?) ? vmss_map : nil
end

# The only time when we don't want to use VMSS ARM template is when
# we're using VMSS Flexible and we explicitly opt out of templates
def use_vmss_template?(node)
  wo = node[:workorder]
  result = true
  dep = wo[:payLoad][:DependsOn]
  vmss_ci = dep.detect do |d|
    %w(Azurescaleset Computeset).include?(d[:ciClassName].split('.').last)
  end if dep

  if vmss_ci && vmss_ci[:ciAttributes][:orchestration] &&
  vmss_ci[:ciAttributes][:orchestration].downcase == 'flexible' &&
  wo[:config] && wo[:config][:skip_vmss_template] == 'true'
    result = false
  end
  result
end

def valid_url?(url)
  begin
    require 'net/http'
    response = Net::HTTP.get_response(URI.parse(url))
    rc = response.is_a?(Net::HTTPSuccess)
    Chef::Log.warn(url + '=> ' + response.inspect) unless rc
    rc
  rescue StandardError => e
    Chef::Log.warn(e.inspect)
    false
  end
end

def rhn_registered?(node, ip = node[:ip])
  key = node[:workorder][:payLoad][:SecuredBy][0][:ciAttributes][:private]
  timeout = 30
  cmd = "sudo timeout #{timeout} /usr/bin/env subscription-manager status"
  result = ssh_exec(ip, initialuser(node), key, cmd)
  if result[:exit_code].zero?
    Chef::Log.info('Compute is registered to RHN')
    return true
  end
  Chef::Log.info('Compute is not registered to RHN')
  false
end

def register_rhn(node)
  key = node[:workorder][:payLoad][:SecuredBy][0][:ciAttributes][:private]
  provider = node['cloud_provider']
  # command execution timeout for the rhn registration command
  timeout = 300
  # Uses CMS vars - sat_register_url, sat_register_server
  if node[:workorder].has_key?('config')
    config = node[:workorder][:config]
    if %i[sat_register_url sat_register_server].all?(&config.method(:key?))
      sat_url = node[:workorder][:config][:sat_register_url]
      if valid_url?(sat_url)
        return true if rhn_registered?(node)

        sat_server = node[:workorder][:config][:sat_register_server]
        rhel_major_version = node[:ostype].split('-')[1].to_i.round(0).to_s
        cmd = "/usr/bin/curl -sSf #{sat_url} > /tmp/satellite_register.sh && \
chmod +x /tmp/satellite_register.sh && \
sudo timeout #{timeout} /tmp/satellite_register.sh -s #{sat_server} -a \
\"#{node["#{provider}_satkey_prefix"]} RHEL #{rhel_major_version} Production\""
        Chef::Log.info("Attempting RHN registration; command: #{cmd}")
        result = ssh_exec(node[:ip], initialuser(node), key, cmd + " || { echo \
RHN registration command exceeded the set timeout 1>&2 ; false; }")
        result[:stdout] = result[:stdout].split("\n").last(25)
        Chef::Log.info(result.inspect.gsub("\r\n", '; '))
        return rhn_registered?(node)
      end
    end
  end
  Chef::Log.warn("Unable to register to RHN due to missing CMS Vars - \
sat_register_url and/or sat_register_server")
  false
end

def deregister_rhn(node)
  wo = node['workorder']
  ci = wo['ci'] || wo['rfcCi']
  ip = (wo.has_key?('resultCi') ? wo['resultCi'] : ci)['ciAttributes']['private_ip']
  if ip.to_s.empty?
    Chef::Log.warn("Unable to get the node's ip from the workorder. De-registration \
failed")
    return false
  end
  return true unless rhn_registered?(node, ip)

  key = node['workorder'][:payLoad][:SecuredBy][0][:ciAttributes][:private]
  timeout = 30
  cmd = "sudo timeout #{timeout} /usr/bin/env subscription-manager unregister"
  Chef::Log.info('De-registering Compute from RHN')
  result = ssh_exec(ip, initialuser(node), key, cmd) unless ip.empty?
  Chef::Log.info(result.inspect.gsub("\r\n", '; '))
  return true if result[:exit_code].zero?
  false
end

def degister_sragent(node)
  res = {}
  timeout = 30
  sragent_exists = false
  wo = node[:workorder]
  ci = wo[:ci] || wo[:rfcCi]
  if wo[:payLoad].has_key?("os")
    os = wo[:payLoad][:os].first
    sragent_exists = !os.nil? && os[:ciAttributes].has_key?("sr_agent_services") && !os[:ciAttributes][:sr_agent_services].empty?
    Chef::Log.info("sragent exists: #{sragent_exists}")
    return if !sragent_exists
  end
  ip = node[:ip] || ci[:ciAttributes][:dns_record]

  user = initialuser(node)
  key = wo[:payLoad][:SecuredBy][0][:ciAttributes][:private]
  cmd = "service sr-agent stop"
  Chef::Log.info("stopping sr-agent service")

  res = ssh_exec(ip, user, key, cmd, timeout,
                 conn: nil, close_conn: true)
  Chef::Log.info("Cmd:#{cmd}, stdout: #{res[:stdout]}, stderr: #{res[:stderr]}")
end

def requiretty_enabled?(nde = node)
  return false if nde[:ostype] =~ /windows/i

  os_version = nde[:ostype].split('-').last
  case nde['provider_class']
  when /azure/i
    ['7.2', '7.0'].include?(os_version)
  else
    false
  end
end

def destroy(compute)
  compute.destroy
rescue Exception => e
  Chef::Log.error("delete failed: #{e.message}")
end

def delete_compute(conn, compute)
  id = compute.id
  Chef::Log.info("destroying server: #{compute.name}, id: #{id}")
  destroy(compute)
  sleep 10

  # retry for 2min for server to be deleted
  ok = false
  attempt = 0
  max_attempts = 6
  while !ok && attempt < max_attempts
    server = conn.servers.get(id)
    if server.nil?
      ok = true
    else
      Chef::Log.info("Attempt: #{attempt}, state: #{server.state}, \
        task state: #{server.os_ext_sts_task_state}. Retrying...")
      destroy(server) if server.os_ext_sts_task_state != 'deleting'
      attempt += 1
      sleep 20
    end
  end

  unless ok
    exit_with_error("Server still not removed after 7 attempts over 2min. \
      Current state: #{conn.servers.get(id).state}")
  end
end

def delete_volume(conn, vol_id)
  vol = conn.volumes.get(vol_id)
  unless vol
    Chef::Log.info("Volume, ID: #{vol_id} doesn't exist or deleted already")
    return
  end

  Chef::Log.info("Deleting volume, ID: #{vol_id}")
  vol.destroy
  attempt = 0
  max_attempts = 12
  while conn.volumes.get(vol_id)
    attempt += 1
    exit_with_error('Failed to delete volume in 120s') if attempt > max_attempts
    Chef::Log.info("Volume delete is still in progress. Rechecking\
      (#{attempt}/#{max_attempts}) completion status in 10s...")
    sleep 10
  end
  Chef::Log.info('Volume deleted successfully')
end

def vm_size_changed? (nu, ciAttrs)
  new_size = nu.size_provider
  if new_size.to_s.strip.empty?
    # Size_provider is null for the provider use regular size
    # get size from cloud
    size_attr = ciAttrs[:size]
    sizemap = JSON.parse(nu.compute_svc[:ciAttributes][:sizemap])
    size_id = sizemap[size_attr]
    if nu.provider =~/azure/
      new_size, = Utils.calc_vm_size(nu, ciAttrs, size_id)
    else
      if size_id.nil? && ciAttrs['size']
        # size id is directly entered into input for specific providers
        size_id = ciAttrs['size']
      end
      new_size = size_id
    end
  end
  curr_size = ciAttrs[:vm_size]
  Chef::Log.debug "Current size : #{curr_size}"
  Chef::Log.debug "New Size: #{new_size}"
  new_size != curr_size
end

def use_ports?
  (get_cms_var('openstack_use_ports') || '').to_s.strip.downcase == 'true'
end

def preserve_ip?
  (get_cms_var('openstack_preserve_ip') || '').to_s.strip.downcase == 'true'
end
