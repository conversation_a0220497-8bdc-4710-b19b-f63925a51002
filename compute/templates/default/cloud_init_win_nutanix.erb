#cloud-config
write_files:
  - path: C:/Windows/Temp/init.ps1
    content: |
      function Get-RandomPassword {
          $password = [Convert]::ToBase64String((1..11 | ForEach-Object { [byte](Get-Random -Min 0 -Max 256) }))
          $password = $password.Substring(0,14)
          return $password
      }

      $username = "<%= @nutanix_vm_username >"

      # Ensure SSH service is installed and configured
      if (!(Get-Service -Name sshd -ErrorAction SilentlyContinue)) {
          & C:\cygwin64\bin\bash.exe --login -i ssh-host-config -y -c "tty ntsec" -N "sshd" -u "cyg_server" -w (Get-RandomPassword)
          netsh advfirewall firewall add rule name="SSH-Inbound" dir=in action=allow enable=yes localport=22 protocol=tcp
      }

      # Start SSH service if not running
      if ((Get-Service -Name sshd).Status -ne "Running") {
          Start-Service -Name sshd
      }

      # Create new user and configure SSH access
      $userPassword = Get-RandomPassword
      net user $username $userPassword /add
      net localgroup Administrators $username /add
      $config_file = "C:/cygwin64/home/<USER>/.ssh/authorized_keys"

      # Ensure .ssh directory exists before creating the authorized_keys file
      $sshDir = [System.IO.Path]::GetDirectoryName($config_file)
      if (!(Test-Path -Path $sshDir)) {
          New-Item -Path $sshDir -ItemType Directory -Force
      }

      # Create authorized_keys file and set appropriate ownership
      if (!(Test-Path -Path $config_file)) { 
          New-Item -Path $config_file -ItemType File -Force 
      }
      Add-Content -Path $config_file -Value "<%= @nutanix_vm_public_key >"
      icacls "C:/cygwin64/home/<USER>" /setowner $username /T /C /q

      $IPType = "IPv4"
      $adapter = Get-NetAdapter | ? {$_.Status -eq "up"}
      $interface = $adapter | Get-NetIPInterface -AddressFamily $IPType
      If ($interface.Dhcp -eq "Disabled") {
        # Remove existing gateway
        If (($interface | Get-NetIPConfiguration).Ipv4DefaultGateway) {
            $interface | Remove-NetRoute -Confirm:$false
        }
        # Enable DHCP
        $interface | Set-NetIPInterface -DHCP Enabled
      }

runcmd:
  - powershell.exe -File C:/Windows/Temp/init.ps1
