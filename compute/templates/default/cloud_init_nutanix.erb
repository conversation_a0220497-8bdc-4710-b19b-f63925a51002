#cloud-config

# Set the hostname
hostname: <%= @nutanix_vm_hostname >

# User Authentication
users:
   - default
   - name: <%= @nutanix_vm_username >
     groups: sudo
     shell: /bin/bash
     lock_passwd: false
     ssh-authorized-keys:
     - <%= @nutanix_vm_public_key >
     sudo: ["ALL=(ALL) NOPASSWD:ALL"]

write_files:
   - path: /etc/netplan/50-cloud-init.yaml
     content: |
        network:
           version: 2  
           renderer: networkd
           ethernets:
              ens3:
                dhcp4: true
                dhcp-identifier: mac

runcmd:
  - sudo netplan apply

power_state:
  delay: "+1"
  mode: reboot
  message: Rebooting after cloud-init
  timeout: 30
  condition: True