#!/bin/bash
# The script to fix ephemeral devices in Azure
# This script will run on resume from hibernation event to fix the ephemeral
# devices. The reason for this fix is hibernation may result in moving the VM
# to another hardware, hence the ephmeral storage device will be recreated.
# This causes the previously mounted storage device to stop working.
# The fix essentially consists of executing dmsetup remove command to clean up
# the mapper metadata, and the re-running the azure mount script.

out=$(lsblk -npro NAME,PKNAME,MOUNTPOINT | grep '\/dev\/mapper\/.*--eph-')

originalIFS=$IFS
IFS=$'\n'

for line in $out
do
  echo "Processing line: $line"
  IFS=$originalIFS arr=( $line )
  LV=${arr[0]}
  PV=${arr[1]}
  mountpoint=${arr[2]}
  echo "Checking LV $LV , located on PV $PV, mounted at $mountpoint"

  if pvs $PV &>/dev/null ; then
    echo "$PV is accessible, nothing to do"
  else
    echo "$PV is not accessible, resetting ephemeral storage"
    umount $mountpoint
    dmsetup remove $LV
  fi
done

script_dir=$(dirname "$0")

for script in "$script_dir"/*.sh
do
  echo "$script"
  if [[ "$script" != "$0" ]]; then
    echo "Executing script: $script"
    $script
  fi
done
