#!/bin/bash
#
# Install ruby and bundle for chef or puppet, oneops user, sshd config
#
set_env()
{
    for ARG in "$@"
    do
      # if arg starts with http then use it to set http_proxy env variable
      if [[ $ARG == http:* ]] ; then
        http_proxy=${ARG/http:/}
        echo "exporting http_proxy=$http_proxy"
        export http_proxy=$http_proxy
      elif [[ $ARG == https:* ]] ; then
        https_proxy=${ARG/https:/}
        echo "exporting https_proxy=$https_proxy"
        export https_proxy=$https_proxy
      elif [[ $ARG == no:* ]] ; then
        no_proxy=${ARG/no:/}
        echo "exporting no_proxy=$no_proxy"
        export no_proxy=$no_proxy
      elif [[ $ARG == rubygems:* ]] ; then
        rubygems_proxy=${ARG/rubygems:/}
        echo "exporting rubygems_proxy=$rubygems_proxy"
        export rubygems_proxy=$rubygems_proxy
      elif [[ $ARG == misc:* ]] ; then
        misc_proxy=${ARG/misc:/}
        echo "exporting misc_proxy=$misc_proxy"
        export misc_proxy=$misc_proxy
      elif [[ $ARG == ruby_binary_path:* ]] ; then
        echo "setting ruby_binary_path to $ARG"
        ruby_binary_path=${ARG/ruby_binary_path:/}
      elif [[ $ARG == ruby_binary_version:* ]] ; then
        echo "setting ruby_binary_version to $ARG"
        ruby_binary_version=${ARG/ruby_binary_version:/}
      elif [[ $ARG == ruby_binary_path_ubuntu_14_04:* ]] ; then
        echo "setting ruby_binary_path_ubuntu_14_04 to $ARG"
        ruby_binary_path_ubuntu_14_04=${ARG/ruby_binary_path_ubuntu_14_04:/}
      fi
    done
}

install_base_centos()
{
  yum -d0 -e0 -y install sudo file make gcc gcc-c++ glibc-devel libgcc libxml2-devel libxslt-devel perl libyaml perl-Digest-MD5 nagios nagios-devel nagios-plugins
  if [ "$major" -lt 7 ] ; then
    yum -d0 -e0 -y install parted
  fi

  if [ "$major" -gt 7 ] ; then
    yum -d0 -e0 -y install network-scripts postfix
  fi
}

install_ruby_centos()
{
  if [ "$cloud_provider" == "azure" ] && [ "$major" -lt 7 ] && [ ! -n "$ruby_binary_path" ]; then
    echo "Centos 6.x VMs on Azure clouds require Ruby 2.0.0"
    echo "Please add ruby_binary_path env variable in compute cloud service"
    exit 1
  fi

  # installing ruby 2.0.0 from a binary for CentOs 6.x if env variable is there
  if [ "$major" -lt 7 ] && [ -n "$ruby_binary_path" ] ; then
    install_ruby_binary_if_not_installed
  else
    packages='ruby ruby-libs ruby-devel rubygems'
    rdoc_package='ruby-rdoc'
    if [ "$major" -gt 7 ]; then
      rdoc_package=''
    fi
    yum -d0 -e0 -y install $packages $rdoc_package
  fi
}

install_ruby_ubuntu_14_04()
{
  if [ -z "$ruby_binary_path_ubuntu_14_04" ]; then
    echo "ERROR: The Path for Ubuntu 14.04 Binaries is Empty."
    exit 1
  fi
  install_ruby_binary $ruby_binary_path_ubuntu_14_04
}

install_ruby_binary_if_not_installed()
{
  if ! is_ruby_exists; then
    install_ruby_binary $ruby_binary_path
  else
    ver=`get_ruby_version`
    if [ "$ver" != "$ruby_binary_version" ] ; then
      yum remove -y ruby
      install_ruby_binary $ruby_binary_path
    fi
  fi
}

is_ruby_exists()
{
  ruby -v > /dev/null 2>&1
  if [ "$?" == "0" ]; then
    return 0
  else
    return 1
  fi
}

get_ruby_version()
{
  #this assumes ruby is already installed and returns the currently installed ruby version
  echo `ruby -e 'print "#{RUBY_VERSION}"'`
}

install_ruby_binary()
{
  # Only take valid formats: tar.gz, rpm
  if [[ $1 != *".tar.gz" ]] && [[ $1 != *".rpm" ]]; then
    echo "ERROR: The ruby_binary_path contains a file of an unrecognized format."
    exit 1
  fi

  wget -q -O ruby-binary $1

  if [[ $1 = *".tar.gz" ]]; then
    tar zxf ruby-binary --strip-components 1 -C /usr
  elif [[ $1 = *".rpm" ]]; then
    rpm -i ruby-binary
  fi

  rm -f ruby-binary
}

set_gem_source()
{
  proxy_exists=`gem source | grep $rubygems_proxy | wc -l`
  if [ $proxy_exists == 0 ] ; then
    echo "adding $rubygems_proxy to gem sources"
    gem source --add $rubygems_proxy
    remove_source http://rubygems.org/
    remove_source https://rubygems.org/
  fi
}

remove_source()
{
  gem_source="$1"
  default_exists=`gem source | grep $gem_source | wc -l`
  if [ $rubygems_proxy != $gem_source ] && [ $default_exists != 0 ] ; then
    echo "removing $gem_source from source list"
    gem source --remove $gem_source
  fi
}


set -e
if ! [ -e /etc/ssh/ssh_host_dsa_key ] ; then
  echo "generating host ssh keys"
  /usr/bin/ssh-keygen -A
fi

if [ -e /etc/oneops-tools-inventory.yml ]
then
    echo "Fast-image detected"
    set -e
else

    echo "No fast-image detected"

    set_env $@

    set -e

    # setup os release variables
    echo "Install ruby and bundle."

    # sles or opensuse
    if [ -e /etc/SuSE-release ] ; then
      zypper -n in sudo rsync file make gcc glibc-devel libgcc ruby ruby-devel rubygems libxml2-devel libxslt-devel perl
      zypper -n in rubygem-yajl-ruby

      # sles
      hostname=`cat /etc/HOSTNAME`
      grep $hostname /etc/hosts
      if [ $? != 0 ]; then
        ip_addr=`ip addr | grep 'state UP' -A2 | tail -n1 | awk '{print $2}' | cut -f1 -d'/' | xargs`
        echo "$ip_addr $hostname" >> /etc/hosts
      fi

    # redhat / centos
    elif [ -e /etc/redhat-release ] ; then
      release=$(cat /etc/redhat-release | grep -o '[0-9]\.[0-9]')
      major=${release%.*}
      install_base_centos
      install_ruby_centos

      # disable selinux
      if [ -e /selinux/enforce ]; then
        echo 0 >/selinux/enforce
        echo "SELINUX=disabled" >/etc/selinux/config
        echo "SELINUXTYPE=targeted" >>/etc/selinux/config
      fi

      # allow ssh sudo w/out tty
      grep -v requiretty /etc/sudoers > /etc/sudoers.t
      mv -f /etc/sudoers.t /etc/sudoers
      chmod 440 /etc/sudoers

    else
      # debian
      pat1="Ubuntu 1[4,6].04"
      pat2="Ubuntu 2[0,2,4].04"
      pat3="Ubuntu 2[2,4].04"
      osv=$(cat /etc/issue)
      export DEBIAN_FRONTEND=noninteractive
      echo "apt-get update ..."

      if [[ $osv =~ $pat1 ]]; then
        apt-get update >/dev/null 2>&1
      else
        apt-get update --allow-releaseinfo-change >/dev/null 2>&1
      fi
      
      if [ $? != 0 ]; then
        echo "apt-get update returned non-zero result code. Usually means some repo is returning a 403 Forbidden. Try deleting the compute from providers console and retrying."
        exit 1
      fi

      if [[ $osv =~ $pat2 ]]; then
        nagios_pkg=nagios4
      else
        nagios_pkg=nagios3
      fi

      if [[ $osv =~ $pat3 ]]; then
        apt-get install -q -y --allow-downgrades libssl3 \
        libssl-dev zlib1g zlib1g-dev $nagios_pkg
      else
        apt-get install -q -y build-essential make libxml2-dev libxslt-dev libz-dev $nagios_pkg
      fi

      apt-get -q -y install ruby ruby-dev

      set +e

      if [[ $(lsb_release -r -s | cut -d. -f1) -gt 16 ]]; then
        apt-get -y -q install rubygems-integration
      fi

      rm -fr /etc/apache2/conf.d/nagios3.conf
      set -e

      # Enable legacy SSL negotiation for clients with latest SSL/TSL (Ubuntu22)
      if [[ $osv =~ "Ubuntu 22.04" ]]; then
        sed -i '/OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION/d' /usr/lib/ruby/3.0.0/openssl.rb
        echo 'OpenSSL::SSL::SSLContext::DEFAULT_PARAMS[:options] |= OpenSSL::SSL::OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION' >> /usr/lib/ruby/3.0.0/openssl.rb
      fi
    fi

    set +e

    #set gem source from compute env variable
    if [ -n "$rubygems_proxy" ]; then
      set_gem_source
    else
      rubygems_proxy="https://rubygems.org"
    fi
    mkdir -p -m 755 /opt/oneops
    echo "$rubygems_proxy" > /opt/oneops/rubygems_proxy

    #Install json gem
    if [ -n "$major" ]; then
      gem_version="1.8.6"
      json_installed=$(gem list ^json$ --version $gem_version -i)
      if [ $json_installed != "true" ]; then
        echo "Installing json gem, version $gem_version"
        gem install json --version $gem_version --no-ri --no-rdoc --quiet
        if [ $? -ne 0 ]; then
          echo "Could not install json gem, version $gem_version" 1>&2
          exit 1
        fi
      fi
    else
      gem list -i ^json$ || gem install --quiet json
    fi

    #set -e
    bundler_installed=$(gem list ^bundler$ -i)
    if [ $bundler_installed != "true" ]; then
      echo "Installing bundler..."
      gem install bundler -v 1.17.3 --bindir /usr/bin --no-ri --no-rdoc --quiet
      if [ $? -ne 0 ]; then
        echo "Could not install bundler gem" 1>&2
        exit 1
      fi
    fi

    #set +e
    perl -p -i -e 's/ 00:00:00.000000000Z//' /var/lib/gems/*/specifications/*.gemspec 2>/dev/null

    # oneops user
    grep "^oneops:" /etc/passwd 2>/dev/null
    if [ $? != 0 ] ; then
      set +e
      echo "*** ADD oneops USER ***"

      # create oneops user & group - deb systems use addgroup
      which addgroup
      if [ $? -eq 0 ] ; then
        addgroup oneops
      else
        groupadd oneops
      fi

      set -e
      useradd oneops -g oneops -m -s /bin/bash
      echo "oneops   ALL = (ALL) NOPASSWD: ALL" >> /etc/sudoers
    else
      echo "oneops user already there..."
    fi
    # sudo permissions for nagios user to run ssh key compliance monitor
    for cmd in cat cp ls timeout
    do
      cmd_path=`which $cmd`
      cmd_exists=`which $cmd >/dev/null 2>&1 && echo true || echo false`
      if [ $cmd_exists == "true" ] ; then
        sudo -l -U nagios $cmd_path >/dev/null 2>&1 || echo "nagios ALL=NOPASSWD: $_" >> /etc/sudoers
      fi
    done
    set -e

    # Configure gem sources for oneops user
    test -f ~/.gemrc && \cp ~/.gemrc /home/<USER>/

    gem_config=~/.config/gem/gemrc
    if test -f "$gem_config"; then
      mkdir -p /home/<USER>/.config/gem
      \cp $gem_config /home/<USER>/.config/gem
    fi

    mkdir -p -m 750 /etc/nagios/conf.d
    mkdir -p -m 755 /opt/oneops/workorder
    mkdir -p -m 750 /var/log/nagios
fi

set -e
me=`logname`
# ssh and components move
if [ "$me" == "oneops" ] ; then
  exit
elif [ "$me" == "root" ] ; then
  cd /root
else
  cd /home/<USER>
fi

me_group=$me
if [ -e /etc/SuSE-release ] ; then
  me_group="users"
fi

# gets rid of the 'only use ec2-user' ssh response
sed -e 's/.* ssh-rsa/ssh-rsa/' .ssh/authorized_keys > .ssh/authorized_keys_
mv .ssh/authorized_keys_ .ssh/authorized_keys
chown $me:$me_group .ssh/authorized_keys
chmod 600 .ssh/authorized_keys

if [ "$me" != "root" ] ; then
  `rsync -a /home/<USER>/.ssh /home/<USER>/`
else
  `cp -r ~/.ssh /home/<USER>/.ssh`
  `cp ~/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys`
fi

if [ "$me" == "idcuser" ] ; then
  echo 0 > /selinux/enforce
  # need to set a password for the rhel 6.3
  openssl rand -base64 12 | passwd oneops --stdin
fi

# On touch update; chown will break nagios if monitor cookbook does not run.
# Still need to chown a few directories
owner=$( ls -ld /opt/oneops/rubygems_proxy | awk '{print $3}' )
if [ "$owner" == "root" ] ; then
  chown -R oneops:oneops /home/<USER>/opt/oneops
  chown -R nagios:nagios /etc/nagios
else
  chown -R oneops:oneops /home/<USER>/.ssh /opt/oneops/workorder /opt/oneops/rubygems_proxy
fi
