{"enabled": true, "username": "infoblox_api_user", "password": "infoblox_api_password", "grid_servers": ["infoblox-grid-master.example.com", "infoblox-grid-member1.example.com", "infoblox-grid-member2.example.com"], "infoblox_url": "https://infoblox-grid-master.example.com", "wapi_version": "v2.12", "description": "Example Infoblox configuration for Nutanix provider. This should be configured in the compute service's infoblox_config attribute as a JSON string."}