name             "Compute"
description      "Installs/Configures compute"
maintainer       "OneOps"
maintainer_email "<EMAIL>"
license          "Apache License, Version 2.0"
depends          "azure"
depends          "shared"
depends          "fqdn"
depends          "gcp_base"
depends          "nutanix_base"

grouping 'default',
  :access => "global",
  :packages => [ 'base', 'mgmt.catalog', 'mgmt.manifest', 'catalog']

grouping 'bom',
  :access => "global",
  :packages => [ 'bom' ]

grouping 'manifest',
  :access => "global",
  :packages => [ 'manifest' ]


# identity
attribute 'instance_name',
  :description => "Instance Name",
  :grouping => 'bom',
  :format => {
    :help => 'Name given to the compute within the cloud provider',
    :important => true,
    :category => '1.Identity',
    :order => 2
  }

attribute 'instance_id',
  :description => "Instance Id",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'Unique Id of the compute instance within the cloud provider',
    :category => '1.Identity',
    :order => 3
  }

attribute 'host_id',
  :description => "Host Id",
  :grouping => 'bom',
  :format => {
    :help => 'Host Id to identify hypervisor / compute node',
    :category => '1.Identity',
    :order => 4
  }

attribute 'hypervisor',
  :description => "Hypervisor",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'Hypervisor identifier.May require admin credentials.',
    :category => '1.Identity',
    :order => 5
  }

attribute 'availability_zone',
  :description => "Availability Zone",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'Assigned Availability Zone',
    :category => '1.Identity',
    :order => 6
  }



attribute 'required_availability_zone',
  :description => "Required Availability Zone",
  :grouping => 'manifest',
  :default => '',
  :format => {
    :help => 'Required Availability Zone - for override of round-robin or random az assignment',
    :category => '1.Identity',
    :order => 7
  }

attribute 'metadata',
  :description => "metadata",
  :grouping => 'bom',
  :data_type => "hash",
  :format => {
    :help => 'Key Value pairs of VM metadata from vm/iaas using fog server.metadata',
    :category => '1.Identity',
    :order => 8
  }

attribute 'tags',
  :description => "tags",
  :grouping => 'bom',
  :data_type => "hash",
  :format => {
    :help => 'Tags',
    :category => '1.Identity',
    :order => 9
  }

attribute 'zone',
          :description => "Fault and Update Domains",
          :grouping => 'bom',
          :data_type => "hash",
          :format => {
              :help => 'Zone',
              :category => '1.Identity',
              :order => 10
          }

attribute 'instance_osdisk_id',
          :description => "Instance OS  Disk Id",
          :grouping => 'bom',
          :format => {
              :help => 'Unique Id of the OS Disk Id attached to Compute within the cloud provider',
              :category => '1.Identity',
              :order => 11
          }

attribute 'instance_nic_id',
          :description => "Instance NIC Id",
          :grouping => 'bom',
          :format => {
              :help => 'Unique Id of the NIC Id attached to Compute within the cloud provider',
              :category => '1.Identity',
              :order => 12
          }

attribute 'set_id',
  :description => 'Set ID',
  :grouping => 'bom',
  :format => {
    :help => 'Resource Id of the compute set - AvSet, VMSS, MIG, etc',
    :category => '1.Identity',
    :order => 13
  }

# state

attribute 'instance_state',
  :description => "Instance State",
  :grouping => 'bom',
  :format => {
    :help => 'Instance status value returned by Cloud Provider. i.e. Fog::Compute::OpenStack::Server.state',
    :category => '2.State',
    :order => 1
  }

attribute 'task_state',
  :description => "Task State",
  :grouping => 'bom',
  :format => {
    :help => 'Task state value returned by Cloud Provider. i.e. os_ext_sts_task_state',
    :category => '2.State',
    :order => 2
  }

attribute 'vm_state',
  :description => "VM State",
  :grouping => 'bom',
  :format => {
    :help => 'VM state value returned by Cloud Provider. i.e. os_ext_sts_vm_state',
    :category => '2.State',
    :order => 3
  }

# resources
attribute 'size',
          :description => 'Instance Size',
          :required    => 'required',
          :default     => 'S',
          :format      => {
            :help     => 'Compute instance sizes are mapped against instance types offered by cloud providers - see provider documentation for details',
            :category => '2.Resources',
            :order    => 1,
            :form     => {
              :field              => 'select',
              :allow_input        => true,
              :options_for_select => [
                ['XS (Micro)', 'XS'],
                ['S (Standard)', 'S'],
                ['M (Standard)', 'M'],
                ['L (Standard)', 'L'],
                ['XL (Standard)', 'XL'],
                ['XXL (Standard)', 'XXL'],
                ['3XL (Standard)', '3XL'],
                ['4XL (Standard)', '4XL'],
                ['L-PERF (Performance Aggregate)', 'L-PERF'],
                ['XL-PERF (Performance Aggregate)', 'XL-PERF'],
                ['XXL-PERF (Performance Aggregate)', 'XXL-PERF'],
                ['3XL-PERF (Performance Aggregate)', '3XL-PERF'],
                ['L-BD (Big Data Optimized)', 'L-BD'],
                ['XL-BD (Big Data Optimized)', 'XL-BD'],
                ['L-NoSQL (NoSQL Flavor)', 'L-NoSQL'],
                ['XL-NoSQL (NoSQL Flavor)', 'XL-NoSQL'],
                ['XXL-NoSQL (NoSQL Flavor)', 'XXL-NoSQL'],
                ['L-LXD (LXD)', 'L-LXD'],
                ['L-IO-LXD (LXD)', 'L-IO-LXD'],
                ['XL-IO-LXD (LXD)', 'XL-IO-LXD'],
                ['XXL-IO-LXD (LXD)', 'XXL-IO-LXD'],
                ['S-MEM-LXD (LXD)', 'S-MEM-LXD'],
                ['L-MEM-LXD (LXD)', 'L-MEM-LXD'],
                ['BM-V1-56 (Standard Baremetal)', 'BM-V1-56'],
                ['BM-V2-56 (Torbit Baremetal)', 'BM-V2-56'],
                ['S-Win (Windows)', 'S-WIN'],
                ['M-Win (Windows)', 'M-WIN'],
                ['L-Win (Windows)', 'L-WIN'],
                ['XL-Win (Windows)', 'XL-WIN'],
                ['XL-Win-LDO (Windows Local Drive Optimized)', 'XL-WIN-LDO'],
                ['S-CPU (Compute Optimized)', 'S-CPU'],
                ['M-CPU (Compute Optimized)', 'M-CPU'],
                ['L-CPU (Compute Optimized)', 'L-CPU'],
                ['XL-CPU (Compute Optimized)', 'XL-CPU'],
                ['XXL-CPU (Compute Optimized)', 'XXL-CPU'],
                ['3XL-CPU (Compute Optimized)', '3XL-CPU'],
                ['4XL-CPU (Compute Optimized)', '4XL-CPU'],
                ['5XL-CPU (Compute Optimized)', '5XL-CPU'],
                ['6XL-CPU (Compute Optimized)', '6XL-CPU'],
                ['7XL-CPU (Compute Optimized)', '7XL-CPU'],
                ['8XL-CPU (Compute Optimized)', '8XL-CPU'],
                ['9XL-CPU (Compute Optimized)', '9XL-CPU'],
                ['10XL-CPU (Compute Optimized)', '10XL-CPU'],
                ['11XL-CPU (Compute Optimized)', '11XL-CPU'],
                ['S-MEM (Memory Optimized)', 'S-MEM'],
                ['M-MEM (Memory Optimized)', 'M-MEM'],
                ['L-MEM (Memory Optimized)', 'L-MEM'],
                ['XL-MEM (Memory Optimized)', 'XL-MEM'],
                ['XXL-MEM (Memory Optimized)', 'XXL-MEM'],
                ['3XL-MEM (Memory Optimized)', '3XL-MEM'],
                ['4XL-MEM (Memory Optimized)', '4XL-MEM'],
                ['5XL-MEM (Compute Optimized)', '5XL-MEM'],
                ['6XL-MEM (Compute Optimized)', '6XL-MEM'],
                ['7XL-MEM (Compute Optimized)', '7XL-MEM'],
                ['8XL-MEM (Compute Optimized)', '8XL-MEM'],
                ['9XL-MEM (Compute Optimized)', '9XL-MEM'],
                ['10XL-MEM (Compute Optimized)', '10XL-MEM'],
                ['11XL-MEM (Compute Optimized)', '11XL-MEM'],
                ['S-IO (Storage Optimized)', 'S-IO'],
                ['M-IO (Storage Optimized)', 'M-IO'],
                ['L-IO (Storage Optimized)', 'L-IO'],
                ['XL-IO (Storage Optimized)', 'XL-IO'],
                ['XXL-IO (Storage Optimized)', 'XXL-IO'],
                ['3XL-IO (Storage Optimized)', '3XL-IO'],
                ['4XL-IO (Storage Optimized)', '4XL-IO'],
                ['L-IO-BFV (Boot From Volume)', 'L-IO-BFV'],
                ['XL-IO-BFV (Boot From Volume)', 'XL-IO-BFV'],
                ['XXL-IO-BFV (Boot From Volume)', 'XXL-IO-BFV'],
                ['XL-BFV (Boot From Volume)', 'XL-BFV'],
                ['XXL-BFV (Boot From Volume)', 'XXL-BFV'],
                ['S-NET (Network Optimized)', 'S-NET'],
                ['M-NET (Network Optimized)', 'M-NET'],
                ['L-NET (Network Optimized)', 'L-NET'],
                ['XL-NET (Network Optimized)', 'XL-NET'],
                ['XXL-NET (Network Optimized)', 'XXL-NET'],
                ['3XL-NET (Network Optimized)', '3XL-NET'],
                ['4XL-NET (Network Optimized)', '4XL-NET'],
                ['3XL-GIT (GIT Custom)', '3XL-GIT'],
                ['4XL-GIT (GIT Custom)', '4XL-GIT'],
                ['XL-GD (Guardium Appliance)', 'XL-GD']
              ]}
          }

attribute 'size_cloud',
          :description => 'Overwrite instance size - direct input',
          :default => '',
          :data_type   => 'cascading_select',
          :format => {
            :help => 'Overwrite instance size with the public cloud specific size, ex. Standard_D1_v3 (Azure), or n1-standard-2 (GCP)',
            :category => '2.Resources',
            :order => 2,
            :form => {
              'field' => 'cascading_select',
              'allow_input' => true,
              'select_values' => {
                  'Azure' => [''],
                  'GCP' => [''],
                  'Openstack' => ['']
                }
              }
            }

attribute 'sku_version',
          :description => 'VM Size Version',
          :default => '',
          :data_type   => 'cascading_select',
          :format => {
            :help => 'Overwrite cloud service size with a different SKU version, ex: v3, v4',
            :category => '2.Resources',
            :order => 3,
            :form => { 'field' => 'cascading_select',
                       'select_values' => {
                         'All Clouds' => [''],
                         'Azure' => ['v4', 'v5'],
                         'GCP' => ['n1','n2']
                       }
            }
          }

attribute 'sku_features',
  :description => 'VM Size Features',
  :default => '',
  :format => {
    :help => 'Enhance cloud service size with extra SKU features, ex: a(AMD), s(Premium Storage), d(ephemeral), etc',
    :category => '2.Resources',
    :order => 4
  }

attribute 'vm_size',
  :description => 'VM Size',
  :grouping => 'bom',
  :format => {
    :important => true,
    :category => '2.Resources',
    :order => 5
  }

attribute 'live_resizing',
  :description => 'Live resizing?',
  :default => 'false',
  :format => {
  :help => 'This feature is only applicable to Azure and may result in VM redeploying and restarting.',
  :category => '2.Resources',
  :form => { 'field' => 'checkbox' },
  :order => 6
}

  attribute 'cores',
  :description => "Number of CPU Cores",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'cores reported by: grep processor /proc/cpuinfo | wc -l',
    :category => '2.Resources',
    :order => 7
  }

attribute 'ram',
  :description => "Ram in MB",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => "ram reported by: free -m | awk '/Mem:/ { print $2 }'",
    :category => '2.Resources',
    :order => 8
  }

attribute 'misc',
  :description => "Miscellaneous settings",
  :grouping => 'bom',
  :data_type => 'hash',
  :default => '{}',
  :format => {
    :help => 'Key Value pairs of miscellaneous VM settings',
    :category => '2.Resources',
    :order => 9
  }

  attribute 'gcp_os_disk_type',
    :description => 'OS/Boot disk type',
    :default => 'pd-standard',
    :format => {
      :category => '21.Resources(GCP Only)',
      :order => 1,
      :form => { 'field' => 'select', 'options_for_select' => [
        ['Standard Disk', 'pd-standard'],
        ['Balanced Disk', 'pd-balanced'],
        ['SSD Disk', 'pd-ssd'],
        ['Extreme Disk', 'pd-extreme']] }
  }

  attribute 'gcp_eph_storage_type',
    :description => 'Ephemeral disk type',
    :default => 'cloud-default',
    :format => {
      :help => 
      'Additional Local Storages',
      :category => '21.Resources(GCP Only)',
      :order => 2,
      :form => { 'field' => 'select', 'options_for_select' => [
        ['Cloud Default', 'cloud-default'],
        ['Standard Disk', 'pd-standard'],
        ['Balanced Disk', 'pd-balanced'],
        ['SSD Disk', 'pd-ssd'],
        ['Local Disk - SCSI', 'local-ssd-scsi'],
        ['Local Disk - NVME', 'local-ssd-nvme']] }
  }

  attribute 'gcp_eph_storage_size',
    :description => "Ephemeral disk size",
    :format => {
      :important => true,
      :help => 'Enter a number to set the size of the storage. Must be 375GB increments for local ssd.',
      :category => '21.Resources(GCP Only)',
      :pattern => "[0-9]+",
      :order => 3,
      :filter => {'all' => {'visible' => 'gcp_eph_storage_type:ne:cloud-default'}}
  }

attribute 'server_image_name',
  :description => "Server Image Name",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'Image name of the provisioned compute',
    :category => '3.Operating System',
    :order => 3
  }

attribute 'server_image_id',
  :description => "Server Image Id",
  :grouping => 'bom',
  :format => {
    :help => 'Image Id of the provisioned compute',
    :category => '3.Operating System',
    :order => 5
  }


# networking
attribute 'private_ip',
  :description => "Private IP",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'Private IP address allocated by the cloud provider',
    :category => '4.Networking',
    :order => 2
  }

attribute 'public_ip',
  :description => "Public IP",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'Public IP address allocated by the cloud provider',
    :category => '4.Networking',
    :order => 3
  }

attribute 'private_dns',
  :description => "Private Hostname",
  :grouping => 'bom',
  :format => {
    :help => 'Private hostname allocated by the cloud provider',
    :category => '4.Networking',
    :order => 4
  }

attribute 'public_dns',
  :description => "Public Hostname",
  :grouping => 'bom',
  :format => {
    :help => 'Public hostname allocated by the cloud provider',
    :category => '4.Networking',
    :order => 5
  }

attribute 'dns_record',
  :description => "DNS Record value used by FQDN",
  :grouping => 'bom',
  :format => {
    :help => 'DNS Record value used by FQDN',
    :category => '4.Networking',
    :order => 6
  }

attribute 'ports',
  :description => "PAT ports",
  :data_type => "hash",
  :default => '{}',
  :format => {
    :help => 'Configure the Port Address Translation PAT from internal ports (key) to external ports (value).',
    :category => '4.Networking',
    :order => 7
  }


attribute 'require_public_ip',
  :description => "Require public IP",
  :default => 'false',
  :format => {
    :help => 'Check if a public IP is required. Setting is used when the compute cloud service public networking type is interface or floating. Option ignnored for Azure.',
    :category => '4.Networking',
    :form => { 'field' => 'checkbox' },
    :order => 10
  }

attribute 'private_ipv6',
  :description => "Private IPv6",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'Private IPv6 address allocated by the cloud provider',
    :category => '4.Networking',
    :order => 11
  }

attribute 'accelerated_flag',
  :description => "Enable Accelerated Networking",
  :default => 'false',
  :format => {
    :help => 'Enables accelerated networking for a NIC.',
    :category => '4.Networking',
    :form => { 'field' => 'checkbox' },
    :order => 12
  }

attribute 'is_baremetal',
  :description => "Baremetal Compute",
  :grouping => 'bom',
  :format => {
    :important => true,
    :help => 'Compute is baremetal',
    :category => '5.Additional Properties',
    :order => 1
  }

attribute 'is_rhn_registered',
  :description => "Is RHN registered?",
  :default => 'false',
  :grouping => 'bom',
  :format => {
    :help => 'To track if a RHEL Compute is registered to RHN',
    :category => '5.Additional Properties',
    :order => 2
  }

attribute 'svc',
  :description => 'Use Service Registration? (DEPRECATED)',
  :default => 'false',
  :format => {
    :category => '6.Service Registration',
    :form => { 'field' => 'checkbox' },
    :order => 1,
    :tip => '<div class="alert alert-danger" style="font-style:normal"><i class="fa fa-warning"><b>WARNING!</b></i> This is now deprecated. Please use new <b>Service Registry Configuration</b> in OS component. Please view the <a href=\'https://confluence.walmart.com/pages/viewpage.action?spaceKey=PLSOAREG&title=Registering+OneOps+Workload+Runtime+Context+via+Service+Registry+Agent\' target=\'_blank\'>user guide</a> for more information.</div>'
}

attribute 'svc_endpoints',
  :description => 'Service Endpoints (DEPRECATED)',
  :data_type => 'array',
  :default => '[]',
  :format => {
    :help => 'Configure Service Endpoints. Format SERVICE_NAME:SERVICE_PORT.',
    :category => '6.Service Registration',
    :order => 2,
    :pattern => '^[a-zA-Z0-9-]+:[0-9]+$',
    :filter => {'all' => {'visible' => 'svc:eq:true'}}
  }

attribute 'svc_registered',
  :grouping => 'bom',
  :description => 'Registered Service Endpoints (DEPRECATED)',
  :data_type => 'array',
  :default => '[]',
  :format => {
    :help => 'Format SERVICE_NAME:SERVICE_PORT',
    :category => '6.Service Registration',
    :order => 3,
    :filter => {'all' => {'visible' => 'svc:eq:true'}}
  }

attribute 'svc_sidecar',
  :description => 'Enable Sidecar Metrics? (DEPRECATED)',
  :default => 'false',
  :format => {
  :help => 'A boolean value indicating if Service Mesh Sidecar is enabled. '\
    'Enable this if one of the services has been onboarded to Service Mesh.',
  :category => '6.Service Registration',
  :form => { 'field' => 'checkbox' },
  :order => 4,
  :filter => {'all' => {'visible' => 'svc:eq:true'}}
}

attribute 'svc_environment',
  :description => 'Consul Environment (DEPRECATED)',
  :default => 'profile',
  :format => {
    :help => 'Overwrite which Consul environment to use for registration. '\
      'Default is based on the Oneops environment profile value, if the '\
      'profile starts with prod - use production, otherwise - non-prod.',
    :category => '6.Service Registration',
    :order => 5,
    :filter => {'all' => {'visible' => 'svc:eq:true'}},
    :form => { 'field' => 'select', 'options_for_select' => [
      ['Profile based','profile'],
      ['Prod','prod'],
      ['Non-Prod','stg']
    ] }
  }

attribute 'svc_host',
  :grouping => 'bom',
  :description => 'Registered Host (DEPRECATED)',
  :data_type => 'text',
  :default => '',
  :format => {
    :help => 'Format: IP ADDRESS ',
    :category => '6.Service Registration',
    :order => 6,
    :filter => {'all' => {'visible' => 'svc:eq:true'}}
  }

recipe 'status', 'Compute Status'
recipe 'reboot', 'Reboot Compute'
recipe 'repair', 'Repair Compute'
recipe 'powercycle', 'Powercycle - HARD reboot'
recipe 'redeploy', 'Redeploy Compute - Only works on azure'
recipe 'enable_hibernation', 'Enable Hibernation (azure only)'
recipe 'deallocate', 'Stop/Deallocate Compute'

recipe 'hibernate',
:description => 'Hibernate Compute',
:args => {
  'enable_hibernation' => {
    'name' => 'Enable hibernation first?',
    'description' => "'yes' => Will attempt to enable hibernation first (VM will be deallocated and started back up) \
      'no' => Will not attempt to enable hibernation first",
    'defaultValue' => 'no',
    'required' => true,
    'pattern' => %w(no yes)
  }
}

recipe 'start', 'Start Compute'

recipe 'poweroff',
:description => 'Power Off Compute',
:args => {
  'poweroff_mode' => {
    'name' => 'Power-off mode',
    'description' => "'graceful' => Will attempt to gracefully shutdown the VM first \
      'force' => Will not attempt to shutdown the VM first",
    'defaultValue' => 'graceful',
    'required' => true,
    'pattern' => %w(graceful force)
  }
}

recipe 'resize',
        :description => 'Change Compute Size - Only works on azure',
              :args       => {
                'vm_port' => {
                  :name => 'vm_port',
                  :description => 'Specify port number to check status',
                  :defaultValue => '',
                  :required => false,
                  :dataType => 'string'
                }
              }
recipe 'add_centrify', 'Register in Centrify'
recipe 'delete_centrify', 'De-register in Centrify'
recipe 'update_tags', 'Update tags (metadata)'
