Description
===========

Installs and configures compute instances.

Requirements
============

Platform: 

* Amazon EC2
* Rackspace
* OpenStack
* CloudStack

Dependencies:


Attributes
==========

* `ostype` - Operating system type, default `Linux 2.6`.
* `mincpu` - Minimum number of CPU cores for the compute instance, default `1`.
* `maxcpu` - Maximum number of CPU cores for the compute instance, default `64`.
* `minmemory` - Minimum amount of memory for the compute instance, default `512 MB`.
* `maxmemory` - Maximum amount of memory for the compute instance, default `16384 MB`.


TODO
====

* all
