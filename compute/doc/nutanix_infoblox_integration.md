# Nutanix-Infoblox Integration

This document describes the Infoblox DHCP reservation integration for Nutanix VMs in the compute cookbook.

## Overview

The Nutanix-Infoblox integration provides automatic DHCP reservation management for Nutanix VMs, including:

- Automatic DHCP reservation creation when VMs are provisioned
- Automatic DHCP reservation cleanup when VMs are deleted
- Batch operations for managing multiple VM reservations
- Enhanced cleanup capabilities for both DNS and DHCP records

## Components

### Libraries

1. **infoblox_client.rb** - Core InfobloxClient class with DHCP reservation functionality
2. **nutanix_infoblox_helper.rb** - Helper module for Nutanix-specific Infoblox operations

### Recipes

1. **add_node_nutanix.rb** - Enhanced to create DHCP reservations during VM provisioning
2. **del_node_nutanix.rb** - Enhanced to remove DHCP reservations during VM deletion
3. **infoblox_fqdn_cleanup.rb** - Enhanced to use new InfobloxClient for DHCP cleanup
4. **nutanix_infoblox_batch.rb** - Batch operations for DHCP reservations
5. **nutanix_infoblox_cleanup.rb** - Dedicated DHCP cleanup recipe

## Configuration

### Compute Service Configuration

Add the following configuration to your compute service's `infoblox_config` attribute as a JSON string:

```json
{
  "enabled": true,
  "username": "infoblox_api_user",
  "password": "infoblox_api_password",
  "grid_servers": [
    "infoblox-grid-master.example.com",
    "infoblox-grid-member1.example.com"
  ],
  "infoblox_url": "https://infoblox-grid-master.example.com",
  "wapi_version": "v2.12"
}
```

### Configuration Parameters

- **enabled**: Boolean to enable/disable Infoblox integration
- **username**: Infoblox API username
- **password**: Infoblox API password
- **grid_servers**: Array of Infoblox grid server hostnames
- **infoblox_url**: Primary Infoblox grid master URL
- **wapi_version**: Infoblox WAPI version (default: v2.12)

## Features

### Automatic DHCP Reservation Management

When a Nutanix VM is created:
1. VM information (hostname, IP, MAC) is extracted
2. The appropriate Infoblox grid server is identified
3. A DHCP reservation is created automatically
4. Services are restarted if required

When a Nutanix VM is deleted:
1. Existing DHCP reservations are located by IP address
2. Reservations are removed from Infoblox
3. Services are restarted if required

### MAC Address Handling

The integration handles MAC addresses in the following priority:
1. Extract from VM metadata if available
2. Generate deterministic MAC from instance ID
3. Use provided MAC address from VM configuration

### Batch Operations

The `nutanix_infoblox_batch` recipe supports:

#### Create Batch Reservations
```bash
# From workorder
chef-client -o compute::nutanix_infoblox_batch -j '{"operation": "create_batch"}'

# From file
chef-client -o compute::nutanix_infoblox_batch -j '{"operation": "create_batch", "vm_data_source": "file", "vm_file": "/tmp/vms.json"}'
```

#### Remove Batch Reservations
```bash
chef-client -o compute::nutanix_infoblox_batch -j '{"operation": "remove_batch", "vm_data_source": "file", "vm_file": "/tmp/vms.json"}'
```

#### Check Status
```bash
chef-client -o compute::nutanix_infoblox_batch -j '{"operation": "status"}'
```

#### Restart Services
```bash
chef-client -o compute::nutanix_infoblox_batch -j '{"operation": "restart_services"}'
```

### VM List File Format

For batch operations using file input, create a JSON file with the following format:

```json
[
  {
    "hostname": "vm1.example.com",
    "ip": "*************",
    "mac": "00:50:56:12:34:56"
  },
  {
    "hostname": "vm2.example.com",
    "ip": "*************",
    "mac": "00:50:56:12:34:57"
  }
]
```

## Error Handling

The integration includes comprehensive error handling:

- Network connectivity issues with Infoblox servers
- Authentication failures
- Missing or invalid configuration
- VM information extraction failures
- DHCP reservation conflicts

Errors are logged and tagged for monitoring and troubleshooting.

## Logging and Monitoring

The integration provides detailed logging with tags for monitoring:

- `***TAG:nutanix_dhcp_cleanup=success/failure`
- `***TAG:nutanix_dhcp_service_restart=success/failure`
- `***TAG:compute_dhcp_cleanup=success/failure`

## Troubleshooting

### Common Issues

1. **Configuration not found**: Ensure `infoblox_config` is properly set in compute service
2. **Authentication failures**: Verify username/password and API permissions
3. **Network connectivity**: Check firewall rules and DNS resolution for grid servers
4. **MAC address generation**: Verify instance ID is available for MAC generation

### Debug Mode

Enable debug logging by setting Chef log level to debug:
```bash
chef-client -l debug -o compute::nutanix_infoblox_batch
```

## Integration with Existing Workflows

The Infoblox integration is designed to work seamlessly with existing Nutanix workflows:

- VM provisioning continues normally even if DHCP reservation fails
- VM deletion proceeds even if DHCP cleanup fails
- Existing `infoblox_fqdn_cleanup` recipe is enhanced but maintains backward compatibility

## Security Considerations

- Store Infoblox credentials securely in the compute service configuration
- Use dedicated API user with minimal required permissions
- Consider network segmentation for Infoblox API access
- Regularly rotate API credentials

## Performance Considerations

- Batch operations use parallel processing for improved performance
- DHCP service restarts are minimized by batching operations
- Network timeouts are configured appropriately for grid operations
