require 'json'
require_relative '../libraries/nutanix_infoblox_helper'

# Include Infoblox helper for batch operations
include NutanixInfobloxHelper

# Nutanix-Infoblox Batch Operations Recipe
# This recipe handles batch DHCP reservation operations for Nutanix VMs

wo = node[:workorder]
cloud_name = wo[:cloud][:ciName]
compute_service = wo[:services][:compute][cloud_name][:ciAttributes]

# Check if Infoblox integration is enabled
unless infoblox_enabled?(compute_service)
  Chef::Log.info("Infoblox integration not enabled, exiting batch operations")
  return
end

# Get operation type from arguments
args = JSON.parse(wo[:arglist] || '{}')
operation = args['operation'] || 'create'
vm_data_source = args['vm_data_source'] || 'workorder'

Chef::Log.info("Starting Nutanix-Infoblox batch operation: #{operation}")

begin
  case operation
  when 'create_batch'
    # Create DHCP reservations for multiple VMs
    vm_list = []
    
    if vm_data_source == 'workorder'
      # Extract VM info from current workorder
      vm_info = extract_vm_info_for_dhcp(node)
      vm_list << vm_info if vm_info[:mac]
    elsif vm_data_source == 'file'
      # Read VM list from file (for bulk operations)
      vm_file = args['vm_file'] || '/tmp/vm_list.json'
      if File.exist?(vm_file)
        vm_list = JSON.parse(File.read(vm_file), symbolize_names: true)
        Chef::Log.info("Loaded #{vm_list.size} VMs from file: #{vm_file}")
      else
        Chef::Log.error("VM file not found: #{vm_file}")
        exit_with_error("VM file not found: #{vm_file}")
      end
    elsif vm_data_source == 'args'
      # Get VM list from arguments
      vm_list = args['vm_list'] || []
      vm_list = vm_list.map { |vm| vm.transform_keys(&:to_sym) }
    end
    
    if vm_list.empty?
      Chef::Log.warn("No VMs found for batch DHCP reservation creation")
      return
    end
    
    # Validate VM list
    valid_vms = vm_list.select do |vm|
      vm[:hostname] && vm[:ip] && vm[:mac]
    end
    
    if valid_vms.size != vm_list.size
      Chef::Log.warn("#{vm_list.size - valid_vms.size} VMs excluded due to missing required fields")
    end
    
    if valid_vms.empty?
      Chef::Log.error("No valid VMs found for batch operation")
      exit_with_error("No valid VMs found for batch operation")
    end
    
    # Create batch DHCP reservations
    success = create_dhcp_reservations_batch(valid_vms, compute_service)
    
    if success
      Chef::Log.info("Successfully completed batch DHCP reservation creation for #{valid_vms.size} VMs")
      puts "***RESULT:batch_operation=create_success"
      puts "***RESULT:processed_vms=#{valid_vms.size}"
    else
      Chef::Log.error("Failed to complete batch DHCP reservation creation")
      exit_with_error("Batch DHCP reservation creation failed")
    end
    
  when 'remove_batch'
    # Remove DHCP reservations for multiple VMs
    vm_list = []
    
    if vm_data_source == 'workorder'
      # Extract VM info from current workorder
      vm_info = extract_vm_info_for_dhcp(node)
      vm_list << vm_info if vm_info[:ip]
    elsif vm_data_source == 'file'
      # Read VM list from file (for bulk operations)
      vm_file = args['vm_file'] || '/tmp/vm_list.json'
      if File.exist?(vm_file)
        vm_list = JSON.parse(File.read(vm_file), symbolize_names: true)
        Chef::Log.info("Loaded #{vm_list.size} VMs from file: #{vm_file}")
      else
        Chef::Log.error("VM file not found: #{vm_file}")
        exit_with_error("VM file not found: #{vm_file}")
      end
    elsif vm_data_source == 'args'
      # Get VM list from arguments
      vm_list = args['vm_list'] || []
      vm_list = vm_list.map { |vm| vm.transform_keys(&:to_sym) }
    end
    
    if vm_list.empty?
      Chef::Log.warn("No VMs found for batch DHCP reservation removal")
      return
    end
    
    # Validate VM list (only IP is required for removal)
    valid_vms = vm_list.select do |vm|
      vm[:ip]
    end
    
    if valid_vms.size != vm_list.size
      Chef::Log.warn("#{vm_list.size - valid_vms.size} VMs excluded due to missing IP address")
    end
    
    if valid_vms.empty?
      Chef::Log.error("No valid VMs found for batch removal operation")
      exit_with_error("No valid VMs found for batch removal operation")
    end
    
    # Remove batch DHCP reservations
    success = remove_dhcp_reservations_batch(valid_vms, compute_service)
    
    if success
      Chef::Log.info("Successfully completed batch DHCP reservation removal for #{valid_vms.size} VMs")
      puts "***RESULT:batch_operation=remove_success"
      puts "***RESULT:processed_vms=#{valid_vms.size}"
    else
      Chef::Log.error("Failed to complete batch DHCP reservation removal")
      exit_with_error("Batch DHCP reservation removal failed")
    end
    
  when 'status'
    # Check status of Infoblox integration
    infoblox_client = initialize_infoblox_client(compute_service)
    if infoblox_client
      begin
        status = infoblox_client.grid_status
        Chef::Log.info("Infoblox grid status: #{status}")
        puts "***RESULT:infoblox_status=#{status['restart_required'] ? 'restart_required' : 'healthy'}"
        puts "***RESULT:grid_status=#{JSON.generate(status)}"
      rescue => e
        Chef::Log.error("Failed to get Infoblox status: #{e.message}")
        puts "***RESULT:infoblox_status=error"
        puts "***RESULT:error_message=#{e.message}"
      end
    else
      Chef::Log.error("Failed to initialize Infoblox client")
      puts "***RESULT:infoblox_status=configuration_error"
    end
    
  when 'restart_services'
    # Restart Infoblox DHCP services
    infoblox_client = initialize_infoblox_client(compute_service)
    if infoblox_client
      begin
        network_info = get_network_info(compute_service)
        if network_info
          ib_network = infoblox_client.network_to_infoblox_format(network_info[:network])
          server = infoblox_client.find_network_server(ib_network)
          
          result = infoblox_client.restart_service(server: server)
          if result
            Chef::Log.info("Successfully restarted Infoblox DHCP services")
            puts "***RESULT:service_restart=success"
          else
            Chef::Log.error("Failed to restart Infoblox DHCP services")
            puts "***RESULT:service_restart=failure"
          end
        else
          Chef::Log.error("No network information available for service restart")
          puts "***RESULT:service_restart=no_network_info"
        end
      rescue => e
        Chef::Log.error("Failed to restart Infoblox services: #{e.message}")
        puts "***RESULT:service_restart=error"
        puts "***RESULT:error_message=#{e.message}"
      end
    else
      Chef::Log.error("Failed to initialize Infoblox client for service restart")
      puts "***RESULT:service_restart=configuration_error"
    end
    
  else
    Chef::Log.error("Unknown operation: #{operation}")
    exit_with_error("Unknown operation: #{operation}. Supported operations: create_batch, remove_batch, status, restart_services")
  end
  
rescue => e
  Chef::Log.error("Error in Nutanix-Infoblox batch operation: #{e.message}")
  Chef::Log.error(e.backtrace.join("\n"))
  exit_with_error("Nutanix-Infoblox batch operation failed: #{e.message}")
end

Chef::Log.info("Completed Nutanix-Infoblox batch operation: #{operation}")
