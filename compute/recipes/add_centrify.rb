ruby_block 'Add Centrify' do
  only_if do
    node[:workorder][:config] && node[:workorder][:config][:sre_centrify] == 'true' &&
      node[:workorder][:payLoad][:os][0][:ciAttributes][:ostype] !~ /windows*/
  end

  block do
    Chef::Log.info('Registering VM in Centrify')

    cen = SharedComponent::Centrify.new(node)
    key_id = cen.key_id
    if key_id.nil? || key_id.empty?
      Chef::Log.info('SshKey does not exist for this Server. Please execute "Migrate Centrify" Action in Keypair component.')
      next
    end

    wo = node[:workorder]
    ci = wo[:ci] || wo[:rfcCi]
    ip = node[:ip] || ci[:ciAttributes][:dns_record]
    ci_name = ci[:ciName].to_s.tr('-', '')
    ci_id = ci[:ciId].to_s.tr('-', '')
    name = "#{ci_name}-#{ci_id}"

    # ############################################ Find/Create the server #############################################
    server_id = cen.get_create_server(name, ip)

    # ############################################ Find/Create the Account #############################################
    cen.get_create_account(server_id) if server_id

    # ############################################ Provision sreadmin user and its public key on the VM #########################
    cen.provision_sreadmin(ip, node)

    Chef::Log.info('Registered VM in Centrify!')
  end
end
