#
# compute::start_node_openstack - start the node
#

require 'fog/openstack'

include_recipe 'shared::set_provider_new'

instance_id = node[:workorder][:ci][:ciAttributes][:instance_id]
server = node[:iaas_provider].servers.get instance_id

if server == nil
  Chef::Log.error("cannot find server by name: "+server_name)
  return false
end

# Check if vm is in active state
return Chef::Log.info('Server ready') if server.state  == 'ACTIVE'

Chef::Log.info("server: "+server.inspect.gsub(/\n|\<|\>|\{|\}/,""))
server.start
Chef::Log.info("start in progress")

server.wait_for(120, 10) { ready? }
Chef::Log.info('Server ready')
