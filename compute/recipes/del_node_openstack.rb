conn = node['iaas_provider']
servers = conn.servers.summary(:name => node[:server_name])

servers.each { |vm| delete_compute(conn, vm) }

os_disk_id = (node[:compute][:instance_osdisk_id] || nil).to_s.strip
delete_volume(conn, os_disk_id) unless os_disk_id.empty?

Chef::Log.info("CMS var, bom.openstack_use_ports is #{use_ports?} and "\
               "bom.openstack_preserve_ip is #{preserve_ip?}")

wo = node[:workorder]
delete_nic = true
if node[:workorder][:rfcCi][:rfcAction] == 'replace' && use_ports? && preserve_ip?
  delete_nic = false
  ci_attr_accel_nw = wo[:rfcCi][:ciAttributes][:accelerated_flag]
  base_attr = wo[:rfcCi][:ciBaseAttributes]
  if base_attr[:accelerated_flag] && base_attr[:accelerated_flag] != ci_attr_accel_nw
    delete_nic = true
  end
end

unless delete_nic
  Chef::Log.info('Skipping delete of port during compute replaces...')
  return
end

cloud_name = wo[:cloud][:ciName]
compute_service = wo[:services][:compute][cloud_name][:ciAttributes]
conn_nw = Fog::Network.new(openstack_creds(compute_service))

# delete port if bom instance_nic_id is not empty and exists in openstack
if node[:compute][:instance_nic_id] &&
   !(port_id = node[:compute][:instance_nic_id]).to_s.strip.empty? &&
   !conn_nw.ports.summary(:id => port_id).empty?
  conn_nw.delete_port(port_id)
  Chef::Log.info("Port, #{port_id} deleted")
end

# delete port matching name if it exists in openstack - to ensure zero orphans
ports_list = conn_nw.ports.summary(:name => node[:server_name])
unless ports_list.empty?
  ports_list_json = JSON.parse(ports_list.to_json)
  port_id = ports_list_json[0]['id']
  conn_nw.delete_port(port_id)
  Chef::Log.info("Port, #{port_id} deleted")
end

# delete ports during cleanup of failed compute deployments
if use_ports? && node[:workorder][:rfcCi][:rfcAction] == 'add_fail_clean' &&
   !node[:port_id].to_s.strip.empty? &&
   !conn_nw.ports.summary(:id => node[:port_id]).empty?
  Chef::Log.info('CMS var, bom.use_ports is true. Cleaning up orhpan ports, if any')
  conn_nw.delete_port(node[:port_id])
  Chef::Log.info("Port, #{node[:port_id]} deleted")
end

puts '***RESULT:instance_nic_id=' unless preserve_ip?
