instance_id = node[:workorder][:ci][:ciAttributes][:instance_id]
server = node[:iaas_provider].servers.get instance_id

if server.nil?
  Chef::Log.error("Cannot find server: #{instance_id}")
  puts '***TAG:repair=compute_not_found'
  exit_with_error('Try replacing the compute or contact the cloud provider'\
                  'to find why it went missing')
end

adminstatus = server.metadata.get('adminstatus')
Chef::Log.info("Server: #{server.inspect.gsub(/\n|\<|\>|\{|\}/,'')}")
Chef::Log.info("Server adminstatus: #{adminstatus.inspect}")

if server.state == 'SHUTOFF'
  Chef::Log.info("Starting up because VM state: #{server.state}")
  puts '***TAG:repair=start'
  server.start
elsif %w(HARD_REBOOT REBOOT).include?(server.state)
  Chef::Log.info("Skipping because VM state: #{server.state}")
elsif adminstatus == 'maintenance'
  Chef::Log.info('Skipping because adminstatus: maintenance')
else
  server.reboot
  Chef::Log.info('Reboot in progress.')
end

time_interval = 180
begin
  server.wait_for(time_interval, 10) { ready? }
  Chef::Log.info('Server ready.')
rescue Fog::Errors::TimeoutError
  exit_with_error("Reboot Failed: Maximum timeout (#{time_interval} seconds"\
                  ") reached. Try replacing the compute.")
end

puts "***RESULT:instance_state=#{server.state}"
task_state = ''
vm_state = ''
if server.class.to_s == 'Fog::Compute::RackspaceV2::Server'
  task_state = server.state_ext || ''
  vm_state = server.state || ''
else
  task_state = server.os_ext_sts_task_state || ''
  vm_state = server.os_ext_sts_vm_state || ''
end

puts "***RESULT:task_state=#{task_state}"
puts "***RESULT:vm_state=#{vm_state}"