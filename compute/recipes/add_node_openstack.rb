# Copyright 2016, Walmart Stores, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

require 'fog/openstack'
require 'json'

# set fog / excon timeouts to 5min
Excon.defaults[:read_timeout] = 300
Excon.defaults[:write_timeout] = 300

#
# supports openstack-v2 auth
#

wo = node[:workorder]
pl = wo[:payLoad]
rfcCi = wo[:rfcCi]
cloud_name = wo[:cloud][:ciName]
compute_service = wo[:services][:compute][cloud_name][:ciAttributes]
accel_nw_enabled = rfcCi[:ciAttributes][:accelerated_flag] == 'true'

fast_flag = 'false'
if wo[:config] && wo[:config][:FAST_IMAGE]
  fast_flag = wo[:config][:FAST_IMAGE]
end

testing_flag = 'false'
if wo[:config] && wo[:config][:TESTING_MODE]
  testing_flag = wo[:config][:TESTING_MODE]
end

Chef::Log.info("FAST_IMAGE flag: #{fast_flag}, TESTING_MODE flag #{testing_flag}")

conn = Fog::Compute.new(openstack_creds(compute_service))
conn_nw = Fog::Network.new(openstack_creds(compute_service))
img_util = ComputeComponent::Image.new(node, conn)

nsPathParts = rfcCi["nsPath"].split("/")
customer_domain = node["customer_domain"]
ostype = img_util.ostype

# Fast Image matching pattern
fast_image_pattern = /[a-zA-Z]{1,20}-#{ostype.gsub(/\./, "")}-\d{4}-v\d{8}-\d{4}/i

#Baremetal condition
additional_properties = JSON.parse(compute_service[:additional_properties])
Chef::Log.info("additional_properties: #{additional_properties.inspect}")

if !additional_properties.nil? && !additional_properties.empty?
  additional_properties.each_pair do |k,v|
    if k.downcase =~/baremetal/ && v.downcase =~/true/
        puts "***RESULT:is_baremetal=" "true"
    end
  end
end

if compute_service.has_key?("initial_user") && !compute_service[:initial_user].empty?
  node.set["use_initial_user"] = true
  initial_user = compute_service[:initial_user]
  # put initial_user on the node for the following recipes
  node.set[:initial_user] = initial_user
end

if ostype =~ /windows*/
  circuit_dir = "/opt/oneops/inductor/circuit-oneops-1"
  user_data_file = "#{circuit_dir}/components/cookbooks/compute/files/default/user_data_script.ps1"
  user_data_script = File.read(user_data_file)
  Chef::Log.info("user_data_script => SET")
end

Chef::Log.debug("Initial USER: #{initial_user}")
Chef::Log.info("compute::add -- name: "+node.server_name+" domain: "+customer_domain+" provider: "+cloud_name)
Chef::Log.debug("rfcCi attrs:"+rfcCi["ciAttributes"].inspect.gsub("\n"," "))

compute_type_hash = {}
compute_type_hash["image_compute_type"] = ""
compute_type_hash["flavor_compute_type"] = ""
compute_type = ""
max_server_create_value = 30
wait_for_initialization = false
max_wait_for_initialize_value = 0

flavor = ""
image = ""
availability_zones = []
availability_zone = ""
manifest_ci = {}
scheduler_hints = {}
server = nil
sleep_factor = 1

ruby_block 'set flavor/image/availability_zone' do
  block do

    if compute_service.has_key?("availability_zones") && !compute_service[:availability_zones].empty?
      availability_zones = JSON.parse(compute_service[:availability_zones])
      availability_zones = availability_zones.select { |az| az =~ /^efaz/ } if accel_nw_enabled
    end
    if availability_zones.size > 0
      case wo[:box][:ciAttributes][:availability]
      when 'redundant'
        instance_index = rfcCi[:ciName].split("-").last.to_i + wo[:box][:ciId]
        index = instance_index % availability_zones.size
        availability_zone = availability_zones[index]
      else
        random_index = rand(availability_zones.size)
        availability_zone = availability_zones[random_index]
      end
    end

    manifest_ci = pl[:RealizedAs][0]

    if manifest_ci["ciAttributes"].has_key?("required_availability_zone") &&
      !manifest_ci["ciAttributes"]["required_availability_zone"].empty?

      availability_zone = manifest_ci["ciAttributes"]["required_availability_zone"]
      Chef::Log.info("using required_availability_zone: #{availability_zone}")
    end

    instance_id = rfcCi[:ciAttributes][:instance_id]
    if rfcCi[:rfcAction] != 'replace' && instance_id && !instance_id.empty?
      server = conn.servers.get(instance_id)
    elsif rfcCi[:rfcAction] != 'replace'
      servers = conn.servers.all(:name => node.server_name)
      if servers
        server = servers.find do |i|
          i.os_ext_sts_task_state != 'deleting' && i.state.upcase != 'DELETED'
        end
      end
      puts "***RESULT:instance_id=#{server.id}" unless server.nil?
    end

    if !server.nil? && server.state.eql?('BUILD')
      msg = "vm #{server.id} is stuck in #{server.state} state"
      Chef::Log.warn("#{msg}. Deleting it...")
      run_context.include_recipe 'compute::delete'
      server = nil
    end

    if server.nil?
      # size / flavor
      flavor = ComputeComponent::SizeManager.new(node).calculate_vm_size[:object]
      Chef::Log.info("flavor: "+flavor.inspect.gsub("\n"," ").gsub("<","").gsub(">",""))

      image = img_util.image
      if img_util.fast_image_flag
        node.set[:fast_image] = true
        node.set[:image_id]   = image.id
      else
        node.set[:fast_image] = false
      end

      Chef::Log.info("image: "+image.inspect.gsub("\n"," ").gsub("<","").gsub(">",""))
      exit_with_error "Invalid compute image provided #{node.image_id} .. Please specify different OS type." if image.nil?

      if image.name.downcase =~ /baremetal/
        compute_type_hash["image_compute_type"] = "baremetal"
      end

      image_metadata_hash = image.metadata.to_hash
      if image_metadata_hash.has_key?("hypervisor_type") && !image_metadata_hash["hypervisor_type"].empty?
        if image_metadata_hash["hypervisor_type"] == "baremetal"
           compute_type_hash["image_compute_type"] = "baremetal"
        end
      end

      if flavor.name.downcase =~ /baremetal/
         compute_type_hash["flavor_compute_type"] = "baremetal"
      end

      if (compute_type_hash.values | compute_type_hash.values ).count  <= 1
        if compute_type_hash.values[0] == "baremetal"
           compute_type = "baremetal"
        end
      else
        exit_with_error "Image and flavor selected do not match for compute request."
      end

      #Set max server create value based on compute_type
      if compute_type == "baremetal"
        max_server_create_value = 360
        node.set["max_retry_count_add"] = 48
      else
        max_server_create_value = 30
        node.set["max_retry_count_add"] = 30
      end
      Chef::Log.debug("max_server_create_value: #{max_server_create_value}")
      Chef::Log.info("max_server_create_value => SET")
      Chef::Log.debug("max_retry_count_add: #{node[:max_retry_count_add]}")
      Chef::Log.info("max_retry_count_add => SET")

      #Set max_wait_for_initialize_value and wait_for_initialization boolean
      if node[:ostype] =~ /windows/
          wait_for_initialization = true
          max_wait_for_initialize_value = 1
      elsif compute_type == "baremetal"
          wait_for_initialization = true
          max_wait_for_initialize_value = 5
      else
          wait_for_initialization = false
          max_wait_for_initialize_value = 0
      end
      Chef::Log.debug("wait_for_initialization: #{wait_for_initialization}")
      Chef::Log.info("wait_for_initialization => SET")
      Chef::Log.debug("max_wait_for_initialize_value: #{max_wait_for_initialize_value}")
      Chef::Log.info("max_wait_for_initialize_value => SET")

    elsif server.state.eql? 'ERROR'
      msg = "vm #{server.id} is stuck in #{server.state} state"
      if defined?(server.fault)
        msg = "vm state: #{server.state} " + "fault message: " + server.fault["message"] + " fault code: " + server.fault["code"].to_s if !server.fault.nil? && !server.fault.empty?
      end
      exit_with_error "#{msg}"
    else
      node.set[:existing_server] = true
      # detects fast image on update
      fast_image = img_util.get_image(server.image['id'])
      node.set[:fast_image] = (!fast_image.nil? && fast_image.name =~ fast_image_pattern)
      image = fast_image
    end

  end
end

# security groups
security_groups = []
ruby_block 'setup security groups' do
  block do

    secgroups = []
    if pl.has_key?("DependsOn") &&
      secgroups = pl[:DependsOn].select{ |ci| ci[:ciClassName] =~ /Secgroup/ }
    end

    secgroups.each do |sg|
      if sg[:rfcAction] != 'delete'
        sgId = sg['ciAttributes']['group_id']
        sgName = sg['ciAttributes']['group_name']
        security_groups.push(sgId)
        Chef::Log.info("Server inspect :::" + server.inspect.gsub("\n",''))

        found_sg = nil
        if server.respond_to?('security_groups')
          found_sg = server.security_groups.detect{ |f| f.name == sgName }
          Chef::Log.info("Security group assigned: #{found_sg.inspect.gsub("\n",'')}")
        end

        #Skip the dynamic sg update for ndc/edc due to OpenStack incompatibility
        if server && server.state == 'ACTIVE' && !found_sg
          # add_security_group to the existing compute instance. works for update calls as well for existing security groups
          begin
            res = conn.add_security_group(server.id, sgName)
            Chef::Log.info("add secgroup response for sg: #{sgName}: #{res.inspect.gsub("\n",'')}")
          rescue Excon::Errors::Error =>e
            msg=''
            case e.response[:body]
            when /\"code\": \d{3}+/
              error_key=JSON.parse(e.response[:body]).keys[0]
              msg = JSON.parse(e.response[:body])[error_key]['message']
              exit_with_error "#{error_key} .. #{msg}"
            else
              msg = JSON.parse(e.response[:body])
              exit_with_error(msg)
            end
          rescue Exception => ex
            msg = ex.message
            exit_with_error(msg)
          end
        end
      end
    end

    # add default security group
    if server
      tenant_id = server.tenant_id
    else
      sgId = secgroups.first['ciAttributes']['group_id']
      tenant_id = conn_nw.security_groups.get(sgId).tenant_id
    end

    options = { :name => 'default', :tenant_id => tenant_id }
    sg = conn_nw.security_groups.all(options).first
    Chef::Log.info("Default secgroup: #{sg.inspect.gsub("\n", ' ')}")
    security_groups.push(sg.id)
    Chef::Log.info("security_groups: #{security_groups.inspect}")
  end
end

metadata = metadata(node)

ruby_block 'create server' do
  block do

    if server.nil?
      Chef::Log.info("server not found - creating")

      # openstack cant have .'s in key_pair name
      node.set["kp_name"] = node.kp_name.gsub(".","-")
      attempted_networks = []

      begin
        Chef::Log.debug("Flavor #{flavor.inspect}")
        if !img_util.is_bfv
          Chef::Log.debug("Using flavor.disk #{flavor.disk}")
        else
          osdisk = [{
            'boot_index': '0',
            'uuid': image.id,
            'source_type': 'image',
            'volume_size': '30',
            'destination_type': 'volume',
            'delete_on_termination': false,
            'tag': 'disk1',
            'disk_bus': 'scsi'
          }]
          os_disk_id = (node[:compute][:instance_osdisk_id] || nil).to_s.strip
          if os_disk_id.empty? || !conn.volumes.get(os_disk_id)
            Chef::Log.info("'Boot from volume' flavors have no OS disk. Will \
              create one separately")
          else
            # for future use; currently supported scenarios will never enter
            # this conditional flow
            Chef::Log.info("'Boot from volume' flavor; existing OS disk: \
              #{os_disk_id} found. Will re-attach")
            osdisk[0][:uuid] = os_disk_id
            osdisk[0][:source_type] = 'volume'
          end
        end

        if flavor.respond_to?('ephemeral')
          metadata[:ephemeral] = flavor.ephemeral.to_s
        end

        network_name, net_id = get_enabled_network(compute_service,attempted_networks)
        Chef::Log.info("CMS var, bom.openstack_use_ports is #{use_ports?} and "\
                       "bom.openstack_preserve_ip is #{preserve_ip?}")
        if use_ports?
          prev_port_id = nil
          if node[:compute][:instance_nic_id] &&
             !node[:compute][:instance_nic_id].to_s.strip.empty?
            prev_port_id = node[:compute][:instance_nic_id]
            Chef::Log.info('Port creation will be skipped if previous port, ' \
                           "#{prev_port_id} exists...")
          end
          port_request_options = {
            name: node.server_name,
            security_groups: security_groups
          }
          if accel_nw_enabled
            port_request_options[:'binding:vnic_type'] = 'direct'
            Chef::Log.info('Enable accelerated networking is checked. Using SR-IOV port...')
          end
          ports_list = if prev_port_id
                         conn_nw.ports.summary(:id => prev_port_id)
                       else
                         conn_nw.ports.summary(:name => node[:server_name])
                       end
          port_id = if ports_list.size.zero?
                      Chef::Log.info('Creating port...')
                      port = conn_nw.create_port(net_id, port_request_options)
                      port.data[:body]['port']['id']
                    else
                      Chef::Log.info('Previous port exists, reusing it...')
                      ports_list_json = JSON.parse(ports_list.to_json)
                      ports_list_json[0]['id']
                    end
          node.set[:port_id] = port_id
        end

        # network / quantum support
        if !net_id.empty?

          Chef::Log.info("metadata: "+metadata.inspect+ " key_name: #{node.kp_name}")

          server_request = {
            :name => node.server_name,
            :image_ref => image.id,
            :flavor_ref => flavor.id,
            :key_name => node.kp_name,
            :metadata => metadata,
            :nics => [ { "net_id" => net_id } ]
          }

          if use_ports?
            server_request[:nics] = [{ 'port_id' => port_id }]
          else
            server_request[:security_groups] = security_groups
          end

          if osdisk
            server_request[:block_device_mapping_v2 ] = osdisk
          end

          Chef::Log.info("ostype: " + ostype)
          if ostype =~ /windows*/
            server_request[:user_data] = user_data_script
          end

          if !availability_zone.empty?
            server_request[:availability_zone] = availability_zone
          end

          if scheduler_hints.keys.size > 0
            server_request[:scheduler_hints] = scheduler_hints
          end

        else
          # older versions of openstack do not allow nics or security_groups
          server_request = {
            :name => node.server_name,
            :image_ref => image.id,
            :flavor_ref => flavor.id,
            :key_name => node.kp_name
          }
        end

        start_time = Time.now.to_i
        server = conn.servers.create server_request
        end_time = Time.now.to_i
        duration = end_time - start_time
        Chef::Log.info("server create returned in: #{duration}s")

        # Extra debugging information
        if pl[:Environment] && pl[:Environment][0] &&
          pl[:Environment][0][:ciAttributes][:debug] == 'true'
          Chef::Log.info("server create request: #{server_request}")
        end

        sleep 10
        server.reload
        sleep_count = 0
        image_size = (image.size/1024.0/1024.0/1024.0).round(1)
        sleep_factor = 4 if image_size > 40
        Chef::Log.info("Image size: #{image_size} GB. Using a sleep factor of \
'#{sleep_factor}' for OpenStack Create Compute API")
        # wait for server to be ready or fail within 5min or an hour.
        while (!server.ready? && server.fault.nil? && sleep_count < max_server_create_value) do
          sleep 10 * sleep_factor
          sleep_count += 1
          server.reload
        end

        if !server.fault.nil? && server.fault.has_key?('message')
          raise Exception.new(server.fault['message'])
        end

      rescue Exception =>e
          message = ""
          case e.message
          when /Failed to allocate the network/
            Chef::Log.info("retrying different network due to: #{e.message}")
            attempted_networks.push(network_name)
            delete_compute(conn, server)
            sleep 5
            retry

          when /Request Entity Too Large/,/Quota exceeded/
            limits = conn.get_limits.body["limits"]
            Chef::Log.info("limits: "+limits["absolute"].inspect)
            message = "openstack quota exceeded to spin up new computes on #{cloud_name} cloud for #{compute_service[:tenant]} tenant"

          when /Volume \S+ did not finish being created/
            message = e.message
            delete_compute(conn, server)
            vol_id = e.message.scan(/Volume (\S+) did not/).flatten
            vol_id.each { |v| delete_volume(conn, v) }
          else
            message = e.message
          end

          if e.respond_to?('response')
            case e.response[:body]
             when /\"code\": 400/
              message = JSON.parse(e.response[:body])['badRequest']['message']
            end
          end

          if message =~ /Invalid imageRef provided/
               Chef::Log.error(" #{node[:ostype]} OS type does not exist. Select the different OS type and retry the deployment")
               message = "Select the different OS type in compute component and retry the deployment"
          end

          if message =~ /availability zone/ && server_request.has_key?(:availability_zone)
            Chef::Log.info("availability zone: #{server_request[:availability_zone]}")
          end

          exit_with_error "#{message}"

      end

      end_time = Time.now.to_i
      duration = end_time - start_time
      Chef::Log.info("server ready in: #{duration}s")
    else
      Chef::Log.info("Server #{server.id} already exists, updating metadata...")
      md = server.metadata.all

      filter = %w(trproductid apmid)
      tags_provider = md.to_hash.select { |k, _| filter.include?(k) }
      tags_wo = tags_wo(node, 'Assembly', filter)

      filter.each do |tag|
        if tags_provider[tag] != tags_wo[tag]
          Chef::Log.info("Tag #{tag} mismatch: at cloud provider - " \
            "#{tags_provider[tag]}, in WorkOrder - #{tags_wo[tag]}")
          conn.update_meta('servers', server.id, tag, tags_wo[tag])
        end
      end
    end

    node.set[:instance_id] = server.id
    puts "***RESULT:availability_zone=#{availability_zone}"
    Chef::Log.info("server: "+server.inspect.gsub("\n"," ").gsub("<","").gsub(">",""))
    puts "***RESULT:instance_id="+server.id
    puts "***RESULT:instance_nic_id=#{port_id}"
    hypervisor = server.os_ext_srv_attr_hypervisor_hostname || ""
    puts "***RESULT:hypervisor="+hypervisor
    puts "***RESULT:instance_state="+server.state
    task_state = server.os_ext_sts_task_state || ""
    puts "***RESULT:task_state="+task_state
    vm_state = server.os_ext_sts_vm_state || ""
    puts "***RESULT:vm_state="+server.os_ext_sts_vm_state
    puts "***RESULT:metadata="+JSON.dump(metadata)
    puts "***RESULT:vm_size="+server.flavor['id']
    os_disk_id = osdisk ? server.volume_attachments[0]['id'] : ''
    # we assume 'boot from volume' flavor if instance_osdisk_id BOM is not empty
    puts "***RESULT:instance_osdisk_id=#{os_disk_id}"
  end
end

private_ip = ''
public_ip = ''

ruby_block 'set node network params' do
  block do
    if server.addresses.has_key? "public"
      public_ip = server.addresses["public"][0]["addr"]
      node.set[:ip] = public_ip
      puts "***RESULT:public_ip="+public_ip
      if ! server.addresses.has_key? "private"
        puts "***RESULT:dns_record="+public_ip
        # in some openstack installs only public_ip is set
        # lets set private_ip to this addr too for other cookbooks which use private_ip
        private_ip = public_ip
        puts "***RESULT:private_ip="+private_ip
      end
    end

    # use private ip if both are set
    if server.addresses.has_key? "private"
      private_ip = server.addresses["private"][0]["addr"]
      node.set[:ip] = private_ip
      puts "***RESULT:private_ip="+private_ip
      puts "***RESULT:dns_record="+private_ip
    end

    # enabled_networks
    if compute_service.has_key?('enabled_networks')
      JSON.parse(compute_service['enabled_networks']).each do |net|
        Chef::Log.info("checking for address by network name: #{net}")
        if server.addresses.has_key?(net)
          addrs = server.addresses[net]
          #check multiple ips (exception: possible to have 2 ip entries (public, private) after initial deployment)
          if addrs.size > 1 && rfcCi['rfcAction'] != 'update'
            Chef::Log.info('Multiple ips returned - Deleting the VM')
            run_context.include_recipe 'compute::delete'
            exit_with_error 'multiple ips returned'
          end
          private_ip = addrs.first["addr"]
          break
        end
      end
    end

    # specific network
    if !compute_service[:subnet].empty?
      network_name = compute_service[:subnet]
      if server.addresses.has_key?(network_name)
        addrs = server.addresses[network_name]
        addrs_map = {}
        # some time openstack returns 2 of same addr
        addrs.each do |addr|
          next if ( addr.has_key? "OS-EXT-IPS:type" && addr["OS-EXT-IPS:type"] != "fixed" )
          ip = addr['addr']
          exit_with_error "The same ip #{ip} returned multiple times" if addrs_map.has_key? ip
          addrs_map[ip] = 1
        end
        private_ip = addrs.first["addr"]
        node.set[:ip] = private_ip
      end
    end

    if private_ip.empty?
      server.addresses.each_value do |addr_list|
        addr_list.each do |addr|
          Chef::Log.info("addr: #{addr.inspect}")
          if addr["OS-EXT-IPS:type"] == "fixed"
            private_ip = addr["addr"]
            node.set[:ip] = private_ip
          end
        end
      end
    end

    if((public_ip.nil? || public_ip.empty?) &&
       rfcCi["rfcAction"] != "add" && rfcCi["rfcAction"] != "replace")
      public_ip = rfcCi[:ciAttributes][:public_ip]
      node.set[:ip] = public_ip
      Chef::Log.info("node ip: " + node.ip)
      Chef::Log.info("Fetching ip from workorder rfc for compute update")
    end

    if rfcCi[:ciAttributes][:require_public_ip] == 'true'
      if compute_service[:public_network_type] == "floatingip"
        server.addresses.each_value do |addr_list|
          addr_list.each do |addr|
            Chef::Log.info("addr: #{addr.inspect}")
            if addr["OS-EXT-IPS:type"] == "floating"
              public_ip = addr["addr"]
              node.set["ip"] = public_ip
            end
          end
        end
        if public_ip.empty?
          floating_ip = conn.addresses.create
          floating_ip.server = server
          public_ip = floating_ip.ip
          node.set["ip"] = public_ip
        end
      end
    end
    # if public_ip is still empty, use private_ip to be public_ip
    if public_ip.empty?
      public_ip = private_ip
      node.set[:ip] = public_ip
    end

    private_ipv6 = ''
    server.addresses.each_value do |addr_list|
      addr_list.each do |addr|
        Chef::Log.info("addr: #{addr.inspect}")
        if addr["OS-EXT-IPS:type"] == "fixed" && addr["version"] == 6
          private_ipv6 = addr["addr"]
          Chef::Log.info("private ipv6 address:#{private_ipv6}")
        end
      end
    end

    # Update mode: if OpenStack API returns an empty private ip, use ip from bom
    if rfcCi['rfcAction'] == 'update'
      if private_ip.strip.empty?
        private_ip = node['compute']['private_ip']
        Chef::Log.info("Private ip:#{private_ip}, from BOM attr")
      end
      if public_ip.strip.empty?
        public_ip = node['compute']['public_ip']
        Chef::Log.info("Public ip:#{public_ip}, from BOM attr")
      end
    end

    puts "***RESULT:public_ip="+public_ip
    dns_record = public_ip
    if dns_record.empty? && !private_ip.empty?
      dns_record = private_ip
    end
    puts "***RESULT:dns_record="+dns_record
    # lets set private_ip to this addr too for other cookbooks which use private_ip
    puts "***RESULT:private_ip="+private_ip
    puts "***RESULT:host_id=#{server.host_id}"
    puts "***RESULT:private_ipv6="+private_ipv6

    if node.ip_attribute == "private_ip"
      node.set[:ip] = private_ip
      Chef::Log.info("setting node.ip: #{private_ip}")
    else
      node.set[:ip] = public_ip
      Chef::Log.info("setting node.ip: #{public_ip}")
    end

    if server.image.has_key? "id"
      server_image_id = server.image["id"]
      server_image = img_util.get_image(server_image_id)
      if ! server_image.nil?
        puts "***RESULT:server_image_id=" + server_image_id
        puts "***RESULT:server_image_name=" + server_image.name
        node.set['image_name'] = server_image.name
      end
    end
  end
end

ruby_block 'catch errors/faults' do
  block do
    # catch faults
    if !server.fault.nil? && !server.fault.empty?
      Chef::Log.error("server.fault: "+server.fault.inspect)
      exit_with_error "NoValidHost - #{cloud_name} openstack doesn't have resources to create your vm" if server.fault.inspect =~ /NoValidHost/
    end
    # catch other, e.g. stuck in BUILD state
    if !node.has_key?("ip") || node.ip.nil?
      msg = "server.state: "+ server.state + " and no ip for vm: #{server.id}"
      exit_with_error "#{msg}"
    end
    # catch other, e.g. VM is in ERROR state
    if "#{server.state}".eql? "ERROR"
      msg = "server.state: "+ server.state + "The newly spawned VM is in ERROR state"
      exit_with_error "#{msg}"
    end
  end
end

#give some time to initialize - max_wait_for_initialize_value min
ruby_block 'wait for initialization' do
  block do
    Chef::Log.debug("Wait to initialize -  #{max_wait_for_initialize_value} min")
    (1..max_wait_for_initialize_value).each do |i|
      sleep 60
    end
  end
end if wait_for_initialization == true

include_recipe "compute::ssh_port_wait"

ruby_block 'handle ssh port closed' do
  block do
    if node[:ssh_port_closed]
      Chef::Log.error("ssh port closed after 5min")
      if rfcCi['rfcAction'] == "add"
        Chef::Log.info("SSH - Deleting the VM")
        run_context.include_recipe 'compute::delete'
      end
      exit_with_error "ssh port closed after 5min"
    end
  end
end
