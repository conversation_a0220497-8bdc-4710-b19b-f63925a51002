wo = node[:workorder]
ci = wo[:ci] || wo[:rfcCi]
ci_attributes = ci[:ciAttributes]
instance_id = ci_attributes[:instance_id]
cloud_name = wo[:cloud][:ciName]

compute_service = wo[:services][:compute][cloud_name][:ciAttributes]
username = compute_service[:username].split("@")
user_name = username[0]
domain = username[1]
password = compute_service[:password]
version = compute_service[:nutanix_endpoint_version]
nutanix_endpoint = compute_service[:nutanix_endpoint]

vm_manager = Nutanix::VMManager.new(nutanix_endpoint, user_name, password, version)

vm_manager.perform_vm_operation(instance_id, node[:workorder][:actionName])


