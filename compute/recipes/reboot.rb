# Copyright 2016, Walmart Stores, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

include_recipe 'shared::set_provider_new'

nu = ComputeComponent::NodeUtils.new(node)
sleep_time = 150
case nu.provider
when /azure/
  include_recipe 'azure::reboot_node'
when /gcp/
  include_recipe 'compute::reboot_node_gcp'
when /nutanix/
  include_recipe 'compute::reboot_node_nutanix'
when /vsphere/
  include_recipe 'vsphere::powercycle_node'
when /openstack/
  include_recipe 'compute::reboot_node_openstack'
else
  Chef::Log.info("Cloud Provider #{nu.provider} is not supported  - \
    wait #{sleep_time} seconds.")
  sleep sleep_time
  return
end
node.set[:ip] = node[:workorder][:ci][:ciAttributes][:private_ip]
# check if port 22 is reachable
Chef::Log.info 'Checking for SSH access via port 22'
key = node[:workorder][:payLoad][:SecuredBy][0][:ciAttributes][:private]
if ssh_ready?(nu.ip_addr, 'oneops', key, 5, sleep_time)
  return
else
  exit_with_error("Cannot ssh to #{nu.ip_addr} after a reboot")
end
