# This is live resizing - changing VM size without replacing the VM.
# There's a few criteria that have to be met in order for this action to work:
# 1) Azure VM (Not supported in Openstack)
# 2) CMS var Compute.resize_action is true for this ns path (false by default)
# 3) Manifest(current) and bom(new) sizes are actually different

nu = ComputeComponent::NodeUtils.new(node)
unless nu.provider == 'azure'
  exit_with_error("VM resizing via an action is not supported for " \
                  "#{nu.provider}.")
end

unless node[:workorder][:config]['resize_action'] == 'true'
  exit_with_error('VM resizing via an action is disabled for this environment.')
end

ciAttr_bom = node[:workorder][:ci][:ciAttributes]
size_mgr = ComputeComponent::SizeManager.new(node)
size_mgr.ci = node[:workorder][:payLoad][:RealizedAs][0]
node.default[:new_size_id] =  size_mgr.calculate_vm_size[:id]

args = ::JSON.parse(node[:workorder][:arglist])
port = args['vm_port']

if (port.nil? || port.empty?) && node[:new_size_id] == ciAttr_bom[:vm_size]
  exit_with_error('VM size is not changing')
end

if nu.provider == 'azure' && ciAttr_bom[:instance_id] =~ /virtualMachineScaleSets/i
  include_recipe 'azure::resize_vmss_node'
elsif nu.provider == 'azure'
  include_recipe 'azure::resize'
end

include_recipe 'compute::status'
include_recipe 'shared::port_wait'
