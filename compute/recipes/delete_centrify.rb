Chef::Log.info('Deregistering VM in Centrify')

cen = SharedComponent::Centrify.new(node)
key_id = cen.key_id
return if key_id.nil? || key_id.empty?

wo = node[:workorder]
ci = wo[:ci] || wo[:rfcCi]
ip = node[:ip] || ci[:ciAttributes][:dns_record]

# Try to find the Account
account_id = cen.get_resource_by_fqdn('VaultAccount', ip)

# Remove Account
cen.delete_account(account_id) if !account_id.nil? && !account_id.empty?

# Try to find the Server
server_id = cen.get_resource_by_fqdn('Server', ip)

# remove server
cen.delete_server(server_id) if !server_id.nil? && !server_id.empty?
