require_relative '../libraries/infoblox_client'

def get_records(record_type,ipaddress)
  return node.infoblox_conn.request(:method => :get, :path => "/wapi/v1.0/record:#{record_type}?ipv4addr=#{ipaddress}")
end

def delete_record(record_ref)
  res = node.infoblox_conn.request(:method => :delete, :path => "/wapi/v1.0/#{record_ref}")
  return res[:status]
end

# Enhanced cleanup using new InfobloxClient for DHCP reservations
def cleanup_dhcp_reservations_with_new_client(delete_vm_ip, cloud_name)
  compute_service = node[:workorder][:services][:compute][cloud_name][:ciAttributes]

  # Check if new Infoblox configuration exists
  infoblox_config = JSON.parse(compute_service[:infoblox_config] || '{}')
  return false if infoblox_config.empty?

  begin
    # Initialize new InfobloxClient
    infoblox_client = InfobloxClient.new(
      username: infoblox_config['username'],
      password: infoblox_config['password'],
      grid_servers: infoblox_config['grid_servers'],
      infoblox_url: infoblox_config['infoblox_url'],
      wapi_version: infoblox_config['wapi_version'] || 'v2.12'
    )

    # Try to find and remove DHCP reservations for this IP
    infoblox_config['grid_servers'].each do |server|
      result = infoblox_client.remove_dhcp_reservation(
        server: server,
        ip_address: delete_vm_ip,
        match_by: :ip
      )

      if result
        Chef::Log.info("Successfully removed DHCP reservation for #{delete_vm_ip} from server #{server}")
        puts "***TAG:compute_dhcp_cleanup=success,#{delete_vm_ip},#{server}"
        return true
      end
    end

    Chef::Log.info("No DHCP reservations found for #{delete_vm_ip}")
    return true

  rescue => e
    Chef::Log.error("Failed to cleanup DHCP reservations with new client: #{e.message}")
    puts "***TAG:compute_dhcp_cleanup=failure,#{delete_vm_ip},#{e.message}"
    return false
  end
end

delete_vm_ip = node[:workorder][:rfcCi][:ciAttributes][:dns_record]
cloud_name = node[:workorder][:cloud][:ciName]
provider_service = node[:workorder][:services][:dns][cloud_name][:ciClassName].split(".").last.downcase

if delete_vm_ip.nil?
  Chef::Log.warn('No DNS Record Found for the VM. Exiting.')
  return
end

# Support only infoblox cleanup
if provider_service.eql?("infoblox")
  include_recipe "fqdn::get_infoblox_connection"

  # Try enhanced DHCP cleanup with new InfobloxClient first
  dhcp_cleanup_success = cleanup_dhcp_reservations_with_new_client(delete_vm_ip, cloud_name)

  # Continue with traditional DNS record cleanup
  dns_types = ['ptr','a']
  if delete_vm_ip =~ Resolv::IPv6::Regex
    dns_types = ['ptr','aaaa']
  end
  dns_types.each do |dns_type|

    res = get_records(dns_type,delete_vm_ip)

    unless res[:status] == 200
      # we are unable to check against infoblox
      # we should warn user that record still may existed.
      Chef::Log.warn("Unable to verify if record type #{dns_type} for #{delete_vm_ip} still existed")
    end

    records = JSON.parse(res[:body])

    Chef::Log.info("No records found type #{dns_type} for #{delete_vm_ip}") if records.size == 0

    records.each do |record|
      record_value = ""
      case dns_type
      when "ptr"
        record_value = "ref => #{record['_ref']}, ptrdname => #{record['ptrdname']}"
      when "a"
        record_value = "ref => #{record['_ref']}, name => #{record['name']}"
      end

      Chef::Log.info("Found record :: #{record_value}")

      res_delete = delete_record(record["_ref"])

      delete_status = (res_delete == 200) ? "success" : "failure"

      if delete_status.eql?("success")
        Chef::Log.info("Sucessfully remove record #{record["_ref"]}")
      else
        Chef::Log.warn("Failed to remove record #{record["_ref"]}")
      end

      puts "***TAG:compute_dns_delete=#{delete_status},#{record_value},#{delete_vm_ip},#{record["_ref"]}"
    end
  end

  # Log overall cleanup status
  if dhcp_cleanup_success
    Chef::Log.info("Enhanced Infoblox cleanup completed successfully")
  else
    Chef::Log.warn("Enhanced Infoblox DHCP cleanup failed, but DNS cleanup may have succeeded")
  end
end