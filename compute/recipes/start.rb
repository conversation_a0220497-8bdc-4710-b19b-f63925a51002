# Copyright 2016, Walmart Stores, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cloud = node[:workorder][:cloud][:ciName]
compute_svc = node[:workorder][:services][:compute][cloud]
provider = compute_svc[:ciClassName].split('.').last.downcase

if provider =~ /azure/
  include_recipe 'azure::start_node'
elsif provider =~ /openstack/
  include_recipe 'compute::start_node_openstack'
elsif provider =~ /gcp/
  include_recipe 'compute::start_node_gcp'
end

include_recipe 'compute::status'
