# Update tags (labels, metadata) for compute related resources
nu = ComputeComponent::NodeUtils.new(node)

include_recipe 'shared::set_provider_new'

ruby_block 'Update tags' do
  only_if { nu.provider == 'openstack' }
  block do
    Chef::Log.info("Updating compute metadata")
    wo = node[:workorder]
    pl = wo[:payLoad]
    rfcCi = wo[:rfcCi] || wo[:ci]
    instance_id = rfcCi[:ciAttributes][:instance_id]
    server = node[:iaas_provider].servers.get instance_id
    md = server.metadata.all

    filter = %w(trproductid apmid)
    tags_provider = md.to_hash.select { |k, _| filter.include?(k) }
    tags_wo = tags_wo(node, :Assembly, filter)

    filter.each do |tag|
      if tags_provider[tag] != tags_wo[tag]
        Chef::Log.info("Tag #{tag} mismatch: at cloud provider - " \
          "#{tags_provider[tag]}, in WorkOrder - #{tags_wo[tag]}")
        node[:iaas_provider].update_meta('servers', instance_id, tag, tags_wo[tag])
      end
    end
  end
end
