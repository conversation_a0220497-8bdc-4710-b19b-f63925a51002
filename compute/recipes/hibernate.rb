cloud = node[:workorder][:cloud][:ciName]
compute_svc = node[:workorder][:services][:compute][cloud]
provider = compute_svc[:ciClassName].split('.').last.downcase

if provider =~ /azure/
  args = ::JSON.parse(node[:workorder][:arglist])
  if args['Enable hibernation first?'] == 'yes'
    include_recipe 'azure::enable_hibernation'
  end
  Utils::vm_action('deallocate',node, 'hibernate'=>'true')
else
  Chef::Log.warn("This operation is not supported on #{provider}")
end

include_recipe 'compute::status'
