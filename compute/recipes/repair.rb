# Copyright 2016, Walmart Stores, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

include_recipe "compute::get_ip_from_ci"
include_recipe "compute::ssh_port_wait"

pl = node[:workorder][:payLoad]
monitoring_enabled = true
if pl.has_key?("Environment") &&
pl[:Environment][0][:ciAttributes].has_key?("monitoring") &&
pl[:Environment][0][:ciAttributes][:monitoring] == "false"
  monitoring_enabled = false
end

nu = ComputeComponent::NodeUtils.new(node)
if nu.provider =~ /azure|gcp|nutanix/
  node.set["use_initial_user"] = true
  node.set['initial_user'] = 'oneops'
end

space = ''
ostype = nu.ostype
windows = ostype =~ /windows/
pack = node[:workorder][:box][:ciAttributes][:pack]
key = node[:workorder][:payLoad][:SecuredBy][0][:ciAttributes][:private]
ip = node[:ip]
user = 'oneops'

ruby_block 'repair node' do
  block do

  # Chec ssh port, service and space available
  if node[:ssh_port_closed] == false
    begin
      ssh_ready = true
      Timeout::timeout(10) do
        # Check the disk size and raise error if it is less than 50MB.
        cmd = windows ? 'fsutil volume diskfree c:' : 'df -BM /'
        output = ssh_exec(ip, user, key, cmd, 5)

        if output[:exit_code] && output[:exit_code] == -1 &&
        output[:stderr] =~ /Timeout::Error:/
          raise Timeout::Error
        else
          std = output[:stdout]
          space = windows ? (std[/\d+/].to_i / 1024 / 1024) : std.split[10][/\d+/]
        end
        Chef::Log.info("Available space: #{space}M")
      end
    rescue Timeout::Error
      Chef::Log.warn('Timed out trying to determine the available disk space')
      ssh_ready = false
    rescue => e
      Chef::Log.info("Unable to find the disk space due to the error: #{e}")
    end

    if space != '' && space.to_i <= 50
      exit_with_error('Disk size is less than 50MB. Exiting.')
    end
  end

  Chef::Log.info("ssh_ready: #{ssh_ready}")

  if ssh_ready && windows && pack == 'iis'
    Chef::Log.info('Performing iisreset on the VM')
    output = ssh_exec(ip, user, key, 'iisreset')
    Chef::Log.info(output[:stdout])
  end

  # If ssh port won't open, or the ssh service is not accessible - reboot
  # otherwise - repair agent
  if node[:ssh_port_closed] == true || !ssh_ready

    #Computes belonging to certain platforms(couchbase) should not be rebooted.
    #this is a temp hack to have list of packs listed here to avoid hard reboots.
    #Note : Powercycle action can not be performed from gui if platform is in
    #patforms_to_exclude list.
    patforms_to_exclude=%W[couchbase]

    if patforms_to_exclude.include? pack
      Chef::Log.info("skipping because #{pack} in platforms to exclude #{patforms_to_exclude}")
      puts "***TAG:repair=skiphardrebootplatformexcluded"
    else
      Chef::Log.info("ssh on #{ip} down - rebooting")
      puts "***TAG:repair=reboot"
      Timeout::timeout(300) do
        run_context.include_recipe("compute::reboot")
      end
    end

  else
    if monitoring_enabled
      Chef::Log.info("ssh on #{ip} up - repairing agent and nagios")
      puts "***TAG:repair=agentrestart"
      Timeout::timeout(150) do
        run_context.include_recipe("compute::repair_agent")
      end
    else
      Chef::Log.info("ssh on #{ip} up - not repairing perf-agent because environment.monitoring=false")
      puts "***TAG:repair=norepairmonitoringdisabled"
    end
  end

 end
end
