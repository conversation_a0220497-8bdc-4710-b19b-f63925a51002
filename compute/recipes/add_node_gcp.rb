node.set[:use_initial_user] = true
node.set[:initial_user] = 'google'
dep = node[:workorder][:payLoad][:DependsOn]
computeset = dep.detect { |d| d['ciClassName'] =~ /Computeset/i }
exit_with_error('Computeset component is required for GCP clouds') if computeset.nil?

mig = GCP::RegionalMIGMgr.new(node)
mig_name = computeset[:ciAttributes]['vmss_name']
mig.mig_name = mig_name
vmids_map = JSON.parse(computeset[:ciAttributes][:vmids_map])
gcp_ord = vmids_map[node[:workorder][:rfcCi][:ciName].split('-').last]
if gcp_ord.nil?
  exit_with_error("GCP VM ordinal was not found. Please make sure to deploy \
  computeset component first. Use touch deploy, if required")
end
gcp_inst_name = "#{mig_name}-#{gcp_ord}"
vm_obj = mig.get_vm(gcp_inst_name)
mig.update_instance_labels(vm_obj, gcp_inst_name)
vm_obj['project_id'] = vm_obj['selfLink'].project
node.set[:ip] = vm_obj['networkInterfaces'][0]['networkIP']

include_recipe 'compute::ssh_port_wait'
include_recipe 'compute::ssh_cmd_for_remote'

puts "***RESULT:instance_name=#{vm_obj['name']}"
puts "***RESULT:vm_size=#{vm_obj['machineType'].getname}"
puts "***RESULT:availability_zone=#{vm_obj['zone'].getname}"
puts "***RESULT:instance_id=#{vm_obj['id']}"
puts "***RESULT:instance_nic_id=#{vm_obj['networkInterfaces'][0]['name']}"
puts "***RESULT:private_ip=#{node['ip']}"
puts "***RESULT:public_ip=#{node['ip']}"
puts "***RESULT:dns_record=#{node['ip']}"
puts "***RESULT:private_ip=#{node['ip']}"
puts "***RESULT:metadata=#{JSON.dump(vm_obj)}"
