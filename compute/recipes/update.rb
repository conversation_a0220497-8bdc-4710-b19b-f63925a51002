#
# Cookbook Name:: compute
# Recipe:: update
#
wo = node[:workorder]
ciAttr = wo[:rfcCi][:ciAttributes]
baseAttr = wo[:rfcCi][:ciBaseAttributes]
sm = ComputeComponent::SizeManager.new(node)

# GCP storage checks
if sm.provider =~ /gcp/
  if (baseAttr.has_key?('gcp_eph_storage_type') &&
      baseAttr[:gcp_eph_storage_type] != ciAttr[:gcp_eph_storage_type]) ||
     (baseAttr.has_key?('gcp_eph_storage_size') &&
      baseAttr[:gcp_eph_storage_size] != ciAttr[:gcp_eph_storage_size])
    exit_with_error("GCP Ephemeral disk type and size does not match with \
      current configuration. Consider replacing compute or change the \
      size/type to original.")
  end
  if baseAttr[:gcp_os_disk_type] &&
     baseAttr[:gcp_os_disk_type] != ciAttr[:gcp_os_disk_type]
    exit_with_error("GCP boot(OS) disk type doesn't match current configuration. \
    Consider replacing compute or change the type to original.")
  end
end

if sm.provider =~ /openstack/
  if baseAttr[:accelerated_flag] &&
     baseAttr[:accelerated_flag] != ciAttr[:accelerated_flag]
    exit_with_error('Compute must be replaced to enable or disable accelerated networking')
  end
end

# Currently we only support live resizing for Azure VMs
if sm.calculate_vm_size[:id] != ciAttr[:vm_size] && (sm.provider !~ /azure/ ||
      ciAttr['live_resizing'] != 'true')
  exit_with_error "Instance size doesn't match with current configuration, \
consider replacing compute or change instance size to original"
elsif !is_propagate_update
  include_recipe 'compute::add'
else
  Chef::Log.info('Skipping compute add')
end

if node[:pack] == 'custom-appliance' && ciAttr.has_key?('server_image_id')
  cms_var_img_id = get_cms_var('appliance_image_id', node)
  vm_img_id = ciAttr[:server_image_id].to_s
  if cms_var_img_id != vm_img_id
    exit_with_error "Image ID(#{cms_var_img_id}) in CMS var, \
    Compute.appliance_image_id does not match current ID. Consider replacing the \
    compute or restore original ID(#{vm_img_id})"
  end
end
