# Check 1: linux-based OS
# Check 2: sreadmin cms var exists and valued
# Check 2: authorized_keys file does not exist for sreadmin user
wo = node[:workorder]
if wo[:config]
  sreadmin_key = wo[:config][:sreadmin_key]
end

ruby_block 'Add sreadmin' do
  only_if do
    node[:ostype] !~ /windows/ && !sreadmin_key.nil? && !sreadmin_key.empty?
  end
  block do
    Chef::Log.info('Provisioning sreadmin user')

    #Provision sreadmin user and its public key on the VM
    ci = wo[:ci] || wo[:rfcCi]
    ip = node[:ip] || ci[:ciAttributes][:dns_record]
    user = initialuser(node)
    key = wo[:payLoad][:SecuredBy][0][:ciAttributes][:private]

    # run check
    res = {}
    timeout = 30
    check_cmd = 'sudo cat /home/<USER>/.ssh/authorized_keys'
    res = ssh_exec(ip, user, key, check_cmd, timeout,
                     conn: nil, close_conn: true)

    if res[:exit_code] == 0 && res[:stdout].chomp == sreadmin_key
      Chef::Log.info('Sreadmin user and its key are already provisioned')
    else
      cmds = []
      cmds << 'sudo groupadd sreadmin || sudo addgroup sreadmin'
      cmds << 'sudo useradd sreadmin -g sreadmin -m -s /bin/bash &&'\
        'sudo -E sh -c \'echo "sreadmin     ALL = (ALL) NOPASSWD: ALL"'\
          '>> /etc/sudoers\''
      cmds << 'sudo mkdir /home/<USER>/.ssh'
      cmds << "sudo -E sh -c 'echo #{sreadmin_key} > "\
        "/home/<USER>/.ssh/authorized_keys'"
      cmds << 'sudo chown -R sreadmin:sreadmin /home/<USER>/.ssh'
      cmds << 'sudo chmod 600 /home/<USER>/.ssh/authorized_keys'

      cmds.each_with_index do |cmd, i|
        act_idx = i + 1
        close_conn = act_idx == cmds.count

        res = ssh_exec(ip, user, key, cmd, timeout,
                       conn: res[:conn], close_conn: close_conn)
        Chef::Log.info("Cmd:#{cmd}, stdout: #{res[:stdout]}, stderr: #{res[:stderr]}")
      end
    end
  end
end
