require 'json'

ci = node[:workorder][:rfcCi] || node[:workorder][:ci]
ciAttr = ci[:ciAttributes]
svc_bom = (node['svc_bom'] || JSON.parse(ciAttr[:svc_registered])).to_a
host_bom = node['host_bom'] || ciAttr[:svc_host]
full_dereg_needed = svc_bom.detect{ |s| s !~ /\// } ||
  ( host_bom && !host_bom.empty? && host_bom !~ /\// ) ||
  ciAttr[:svc] != 'true'

# De-register old-format services and host
# Since we cannot definitely say which env the service/host was registered to
# - we'll de-register the service/host in both stg and prod
ruby_block 'Service Deregistration' do
  only_if { full_dereg_needed }
  block do
    Chef::Log.debug('Including service deregistration')
    run_context.include_recipe 'compute::service_deregistration'
    svc_bom = (node['svc_bom'] || JSON.parse(ciAttr[:svc_registered])).to_a
    host_bom = node['host_bom'] || ciAttr[:svc_host]
  end
end

ruby_block 'Service Registration' do
  only_if { ciAttr[:svc] == 'true' }
  block do
    sr = ComputeComponent::ServiceRegistration.new(node)
    ip = sr.ip
    env = sr.consul_env

    # Host format: env/ip-address
    host_manifest = env + '/' + ip
    # Service format: env/ip-address/service:port
    svc_manifest0 = JSON.parse(ciAttr[:svc_endpoints])
    svc_manifest = svc_manifest0.map{ |s| [env, ip, s].join('/') }
    # Add sidecar service
    if ciAttr[:svc_sidecar] == 'true' && svc_manifest && svc_manifest.size > 0
      svc_manifest.push([env, ip, 'sm2-envoy:15090'].join('/'))
    end

    svc_reg = (svc_manifest - svc_bom).uniq
    svc_dereg = (svc_bom - svc_manifest).uniq

    # De-register host if there's no services to be registered
    if svc_manifest.empty? || host_bom != host_manifest
      host_dereg = host_bom
    end

    Chef::Log.debug("host_bom: #{host_bom}")
    Chef::Log.debug("host_manifest: #{host_manifest}")
    Chef::Log.debug("svc_bom: #{svc_bom}")
    Chef::Log.debug("svc_manifest: #{svc_manifest}")
    Chef::Log.info("Services to register: #{svc_reg}")
    Chef::Log.info("Services to de-register: #{svc_dereg}")

    #de-register services
    svc_dereg.each do |svc|
      Chef::Log.info("De-Registering service: #{svc}")
      result_dereg = sr.deregister_service(svc)
      svc_bom.delete(svc) if result_dereg
    end

    #de-register host
   if host_dereg && !host_dereg.empty?
     Chef::Log.info("De-Registering host: #{host_dereg}")
     result_dereg = sr.deregister_host(host_dereg)
     host_bom = '' if result_dereg
   end

    # Register host
    if host_bom.nil? || host_bom.empty?
      Chef::Log.info("Registering host: #{host_manifest}")
      res_host = sr.register_host
      host_bom = host_manifest if res_host
    end

    # Registering services
    svc_reg.each do |svc|
      if host_bom && !host_bom.empty?
        Chef::Log.info("Registering service: #{svc}")
        result_reg = sr.register_service(svc) 
        svc_bom.push(svc) if result_reg
      end
    end

    puts "***RESULT:svc_registered=#{svc_bom.uniq}"
    puts "***RESULT:svc_host=#{host_bom}"
  end
end
