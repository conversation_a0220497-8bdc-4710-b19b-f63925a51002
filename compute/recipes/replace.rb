# Copyright 2016, Walmart Stores, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# Cookbook Name:: compute
# Recipe:: replace
#

require 'excon'
extend Fqdn::Base
Chef::Resource::RubyBlock.send(:include, Fqdn::Base)

include_recipe "fqdn::get_infoblox_connection"

cloud = node[:workorder][:cloud][:ciName]
compute_svc = node[:workorder][:services][:compute][cloud]
provider = compute_svc[:ciClassName].split('.').last.downcase

# refactoring azure specific recipe to an azure folder to make it easier to manage all the files.
if provider =~ /azure/ && vmss_map(node) && use_vmss_template?(node)
  Chef::Log.warn('Not running compute delete for VMSS')
  #execute infoblox_cleanup for azure vmss
  include_recipe 'compute::infoblox_fqdn_cleanup'
else
  include_recipe 'compute::delete'
end
include_recipe 'compute::add'
