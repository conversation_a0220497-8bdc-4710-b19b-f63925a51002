cloud = node[:workorder][:cloud][:ciName]
compute_svc = node[:workorder][:services][:compute][cloud]
provider = compute_svc[:ciClassName].split('.').last.downcase

if provider =~ /azure/
  skip_shutdown = 'false'
  args = ::JSON.parse(node[:workorder][:arglist])
  skip_shutdown = 'true' if args['Power-off mode'] == 'force'
  Utils::vm_action('powerOff', node, 'skipShutdown' => skip_shutdown)
elsif provider =~ /openstack/
  include_recipe 'compute::poweroff_node_openstack'
elsif provider =~ /gcp/
  include_recipe 'compute::poweroff_node_gcp'
else
  Chef::Log.warn("This operation is not supported on #{provider}")
end

include_recipe 'compute::status'
