require 'json'
require_relative '../libraries/nutanix_infoblox_helper'

# Include Infoblox helper for cleanup operations
include NutanixInfobloxHelper

# Nutanix-Infoblox DHCP Cleanup Recipe
# This recipe handles cleanup of DHCP reservations for Nutanix VMs

wo = node[:workorder]
cloud_name = wo[:cloud][:ciName]
compute_service = wo[:services][:compute][cloud_name][:ciAttributes]

# Get VM IP address for cleanup
delete_vm_ip = wo[:rfcCi][:ciAttributes][:dns_record] || wo[:rfcCi][:ciAttributes][:private_ip]

if delete_vm_ip.nil? || delete_vm_ip.empty?
  Chef::Log.warn('No IP address found for DHCP cleanup. Exiting.')
  return
end

Chef::Log.info("Starting Nutanix-Infoblox DHCP cleanup for IP: #{delete_vm_ip}")

# Check if Infoblox integration is enabled
unless infoblox_enabled?(compute_service)
  Chef::Log.info("Infoblox integration not enabled, skipping DHCP cleanup")
  return
end

begin
  # Initialize Infoblox client
  infoblox_client = initialize_infoblox_client(compute_service)
  unless infoblox_client
    Chef::Log.error("Failed to initialize Infoblox client")
    return
  end

  # Get network information
  network_info = get_network_info(compute_service)
  unless network_info
    Chef::Log.error("No network information available for DHCP cleanup")
    return
  end

  # Find the appropriate Infoblox server for this network
  ib_network = infoblox_client.network_to_infoblox_format(network_info[:network])
  server = infoblox_client.find_network_server(ib_network)
  
  Chef::Log.info("Found Infoblox server #{server} managing network #{ib_network}")

  # Try to find and remove DHCP reservations by IP address
  Chef::Log.info("Looking for DHCP reservations for IP: #{delete_vm_ip}")
  
  # First try to get reservation details for logging
  query = URI.encode_www_form(ipv4addr: delete_vm_ip, _return_as_object: 1)
  url = URI("https://#{server}/wapi/#{infoblox_client.wapi_version}/fixedaddress?#{query}")
  
  begin
    reservations = infoblox_client.send(:infoblox_get, url)
    
    if reservations.is_a?(Array) && !reservations.empty?
      reservations.each do |reservation|
        hostname = reservation['name'] || 'unknown'
        mac = reservation['mac'] || 'unknown'
        ref = reservation['_ref']
        
        Chef::Log.info("Found DHCP reservation: hostname=#{hostname}, ip=#{delete_vm_ip}, mac=#{mac}, ref=#{ref}")
        
        # Remove the reservation
        result = infoblox_client.remove_dhcp_reservation(
          server: server,
          ip_address: delete_vm_ip,
          mac: mac,
          hostname: hostname,
          match_by: :ip
        )
        
        if result
          Chef::Log.info("Successfully removed DHCP reservation for #{hostname} (#{delete_vm_ip})")
          puts "***TAG:nutanix_dhcp_cleanup=success,#{hostname},#{delete_vm_ip},#{mac},#{ref}"
        else
          Chef::Log.warn("Failed to remove DHCP reservation for #{hostname} (#{delete_vm_ip})")
          puts "***TAG:nutanix_dhcp_cleanup=failure,#{hostname},#{delete_vm_ip},#{mac},#{ref}"
        end
      end
    else
      Chef::Log.info("No DHCP reservations found for IP: #{delete_vm_ip}")
      puts "***TAG:nutanix_dhcp_cleanup=not_found,#{delete_vm_ip}"
    end
    
  rescue => e
    Chef::Log.error("Error during DHCP reservation lookup: #{e.message}")
    puts "***TAG:nutanix_dhcp_cleanup=lookup_error,#{delete_vm_ip},#{e.message}"
  end

  # Also try to remove by hostname if we have VM instance information
  if wo[:rfcCi][:ciAttributes][:instance_name]
    hostname = wo[:rfcCi][:ciAttributes][:instance_name]
    Chef::Log.info("Also trying cleanup by hostname: #{hostname}")
    
    result = infoblox_client.remove_dhcp_reservation(
      server: server,
      ip_address: delete_vm_ip,
      hostname: hostname,
      match_by: :hostname
    )
    
    if result
      Chef::Log.info("Successfully removed DHCP reservation by hostname: #{hostname}")
      puts "***TAG:nutanix_dhcp_cleanup_by_hostname=success,#{hostname},#{delete_vm_ip}"
    else
      Chef::Log.info("No DHCP reservation found by hostname: #{hostname}")
      puts "***TAG:nutanix_dhcp_cleanup_by_hostname=not_found,#{hostname},#{delete_vm_ip}"
    end
  end

  # Check if we should restart DHCP services
  if infoblox_client.restart_required?
    Chef::Log.info("DHCP service restart required after cleanup")
    
    begin
      infoblox_client.restart_service(server: server)
      Chef::Log.info("Successfully restarted DHCP services")
      puts "***TAG:nutanix_dhcp_service_restart=success,#{server}"
    rescue => e
      Chef::Log.warn("Failed to restart DHCP services: #{e.message}")
      puts "***TAG:nutanix_dhcp_service_restart=failure,#{server},#{e.message}"
    end
  else
    Chef::Log.info("No DHCP service restart required")
  end

rescue => e
  Chef::Log.error("Error in Nutanix-Infoblox DHCP cleanup: #{e.message}")
  Chef::Log.error(e.backtrace.join("\n"))
  puts "***TAG:nutanix_dhcp_cleanup=error,#{delete_vm_ip},#{e.message}"
end

Chef::Log.info("Completed Nutanix-Infoblox DHCP cleanup for IP: #{delete_vm_ip}")
