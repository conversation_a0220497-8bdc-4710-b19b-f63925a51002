require 'json'
require_relative '../libraries/nutanix_infoblox_helper'

wo = node[:workorder]
rfc_ci = wo[:rfcCi]
is_replace = rfc_ci[:rfcAction] == 'replace'
ci_attributes = rfc_ci[:ciAttributes]
cloud_name = wo[:cloud][:ciName]

compute_service = wo[:services][:compute][cloud_name][:ciAttributes]
username = compute_service[:username].split("@")
user_name = username[0]
password = compute_service[:password]
version = compute_service[:nutanix_endpoint_version]
nutanix_endpoint = compute_service[:nutanix_endpoint]

# Include Infoblox helper for DHCP reservation cleanup
include NutanixInfobloxHelper

begin
  instance_id = ci_attributes[:instance_id] || node[:instance_id]

  Chef::Log.info("Instance ID: #{instance_id}")

  # Remove Infoblox DHCP reservation if enabled (before deleting VM)
  if infoblox_enabled?(compute_service)
    Chef::Log.info("Removing Infoblox DHCP reservation for VM #{instance_id}")
    dhcp_success = remove_dhcp_reservation_for_vm(node, compute_service)
    if dhcp_success
      Chef::Log.info("Successfully removed Infoblox DHCP reservation")
    else
      Chef::Log.warn("Failed to remove Infoblox DHCP reservation - continuing with VM deletion")
    end
  else
    Chef::Log.info("Infoblox integration not enabled, skipping DHCP reservation cleanup")
  end

  # Initialize the VMManager
  vm_manager = Nutanix::VMManager.new(nutanix_endpoint, user_name, password, version)

  # Delete the VM
  vm_manager.delete_vm(instance_id, is_replace)

  Chef::Log.info("VM with ID #{instance_id} deleted successfully.")

rescue StandardError => e
  Chef::Log.error("An error occurred in Nutanix delete node recipe: #{e.message}")
  Chef::Log.error(e.backtrace.join("\n"))
  exit_with_error(e.message)
end
