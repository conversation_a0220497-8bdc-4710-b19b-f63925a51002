require 'json'
require 'erb'
require 'resolv'

nu = ComputeComponent::NodeUtils.new(node)
os_type, os_version = nu.ostype.split('-')

unless ((os_type == 'ubuntu' && os_version.to_i >= 22) || os_type =~ /windows/)
  exit_with_error("OS type #{os_type} and OS version #{os_version} is not supported in Nutanix")
end

wo = node[:workorder]
pl = wo[:payLoad]
rfcCi = wo[:rfcCi]

return if rfcCi['rfcAction'] == 'update'

cloud_name = wo[:cloud][:ciName]
compute_service = wo[:services][:compute][cloud_name][:ciAttributes]

username = compute_service[:username].split('@')
user_name, user_domain = username[0], username[1]
password = compute_service[:password]
version = compute_service[:nutanix_endpoint_version]

domains = JSON.parse(compute_service[:domains])
Chef::Log.info("domains: #{domains}")

domain = nu.production? ? domains['prod'] : domains['lab']

nutanix_endpoint = compute_service[:nutanix_endpoint]

server_name = node[:server_name]
Chef::Log.info("server_name: #{server_name}")

node.set[:instance_name] = server_name

sub_network = JSON.parse(compute_service[:subnetwork]).first.split(':')
subnet_uuid, subnet_name = sub_network

begin

  initial_user = os_type =~ /windows/ ? "oneops" : node[:initial_user]
  template_file = os_type =~ /windows/ ? "cloud_init_win_nutanix.erb" : "cloud_init_nutanix.erb"
  node.set[:use_initial_user] = os_type =~ /windows/ ? false : true

  public_key = pl[:SecuredBy][0][:ciAttributes][:public].chomp
  cookbook = run_context.cookbook_collection['compute']
  template_path = cookbook.preferred_filename_on_disk_location(node, :templates, template_file)

  user_data = File.read(template_path)
  cloudinit_content = user_data.gsub(/<%= @nutanix_vm_public_key >/, public_key)
                               .gsub(/<%= @nutanix_vm_hostname >/, server_name)
                               .gsub(/<%= @nutanix_vm_username >/, initial_user)

  node.set[:initial_user] = initial_user

  cluster_reference = JSON.parse(compute_service[:clusters]).first
  enable_autocluster = get_cms_var('enable_auto_cluster') || true

  ostype = if node[:workorder][:payLoad].key?("os")
             node[:workorder][:payLoad][:os].first[:ciAttributes][:ostype]
           else
             Chef::Log.warn("missing os payload - using default-cloud")
             'default-cloud'
           end

  imagemap = JSON.parse(compute_service[:imagemap])
  image_uuid = imagemap[ostype]

  sizemap = JSON.parse(compute_service[:sizemap])
  size = sizemap[rfcCi[:ciAttributes][:size]]
  vm_cpu, vm_memory = size.split(':').map(&:to_i)
  vm_memory *= 1024

  vm_manager = Nutanix::VMManager.new(nutanix_endpoint, user_name, password, version)

  Chef::Log.info("VM creation started...")

  vm_creation_result = vm_manager.create_vm(
    server_name,
    cloudinit_content,
    { image_uuid: image_uuid },
    { subnet_uuid: subnet_uuid, subnet_name: subnet_name },
    enable_autocluster,
    cluster_reference,
    vm_cpu,
    vm_memory
  )

  vm_uuid = vm_creation_result[:vm_uuid]
  ip_addresses = vm_creation_result[:ip_addresses]
  node.set[:instance_id] = vm_uuid


  Chef::Log.info("VM created successfully: #{vm_uuid}")

  if ip_addresses.any?
    ip_address = ip_addresses.first
    Chef::Log.info("VM IP address: #{ip_address}")
    node.set[:ip] = ip_address
  else
    exit_with_error("No IP addresses found for VM #{vm_uuid}.")
  end


  include_recipe 'compute::ssh_port_wait'
  include_recipe 'compute::ssh_cmd_for_remote'

  puts "***RESULT:instance_name=#{server_name}"
  puts "***RESULT:vm_size=#{size}"
  puts "***RESULT:instance_id=#{vm_uuid}"
  puts "***RESULT:private_ip=#{ip_address}"
  puts "***RESULT:public_ip=#{ip_address}"
  puts "***RESULT:dns_record=#{ip_address}"
rescue StandardError => e
  Chef::Log.error("An error occurred in add nutanix recipe: #{e.message}")
  Chef::Log.error(e.backtrace.join("\n"))
  exit_with_error(e.message)
end