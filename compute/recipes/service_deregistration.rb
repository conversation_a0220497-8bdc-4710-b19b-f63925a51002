require 'json'
ciAttr = (node[:workorder][:rfcCi] || node[:workorder][:ci])[:ciAttributes]
svc_bom = JSON.parse(ciAttr[:svc_registered])
host_bom = ciAttr[:svc_host]
ip_bom = host_bom.split('/').last if host_bom

Chef::Log.info("Services to de-register: #{svc_bom}")

require 'net/http'
sr = ComputeComponent::ServiceRegistration.new(node)

#de-register services
svc_dereg = svc_bom.dup
svc_dereg.each do |svc|
  if svc.split('/').size == 1
    Chef::Log.info("De-Registering service w/o env: #{svc}")
    res1 = sr.deregister_service(['stg', ip_bom, svc].join('/'))
    res2 = sr.deregister_service(['prod', ip_bom, svc].join('/')) if res1
    svc_bom.delete(svc) if res1 && res2
  else
    Chef::Log.info("De-Registering service: #{svc}")
    res = sr.deregister_service(svc)
    svc_bom.delete(svc) if res
  end
end

node.default['svc_bom'] = svc_bom

#de-register host
if host_bom && !host_bom.empty?
  Chef::Log.info("De-Registering host: #{host_bom}")
  if host_bom =~ /\//
    result_dereg = sr.deregister_host(host_bom)
    host_bom = '' if result_dereg
  else
    res1 = sr.deregister_host('stg/' + ip_bom)
    res2 = sr.deregister_host('prod/' + ip_bom) if res1
    host_bom = '' if res1 && res2
  end
end

node.default['host_bom'] = host_bom

puts "***RESULT:svc_registered=#{svc_bom.uniq}"
puts "***RESULT:svc_host=#{host_bom}"
