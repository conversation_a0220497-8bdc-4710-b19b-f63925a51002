#
# compute::poweroff_node_opentack - poweroff the node
#

include_recipe 'shared::set_provider_new'

instance_id = node[:workorder][:ci][:ciAttributes][:instance_id]
server = node[:iaas_provider].servers.get instance_id

if server == nil
  Chef::Log.error('cannot find server by name: '+server_name)
  return false
end

# Check if vm is in active state
return Chef::Log.info('Server powered off') if server.state == 'SHUTOFF'

Chef::Log.info("server: "+server.inspect.gsub(/\n|\<|\>|\{|\}/,""))
server.stop
Chef::Log.info("stop in progress")


server.wait_for(120, 10) { server.state == 'SHUTOFF' }
Chef::Log.info('Server powered off.')
