nu = ComputeComponent::NodeUtils.new(node)
ostype, os_version = nu.ostype.split('-')

ruby_block 'Install reset_ephemeral script' do
  only_if { nu.provider.downcase == 'azure' && ostype !~ /windows/ }
  block do
    run_context.include_recipe 'azure::install_reset_ephemeral'
  end
end

ruby_block 'Unmount resource disk in Ubuntu 20+' do
  block do
    if ostype =~ /ubuntu/ && (%w[azure openstack].include? nu.provider) &&
       Gem::Version.new(os_version) >= Gem::Version.new('20')
      key = node[:workorder][:payLoad][:SecuredBy][0][:ciAttributes][:private]
      pattern = '^\/dev\/.*\/mnt.*comment=cloudconfig'
      part_cmd = "grep '#{pattern}' /etc/fstab | awk '{print $2}'"
      part_res = ssh_exec(node[:ip], initialuser(node), key, part_cmd)
      if part_res[:exit_code].zero? && part_res[:stdout] && part_res[:stdout] =~ /^\//
        mount_name = part_res[:stdout].chomp
        Chef::Log.info("Unmounting #{nu.provider} resource disk in Ubuntu20+ #{mount_name}")
        update_fstab_cmd = "sudo sed '/#{pattern}/d' -i /etc/fstab"
        update_fstab_res = ssh_exec(node[:ip], initialuser(node), key, update_fstab_cmd)
        if update_fstab_res[:exit_code].zero?
          run_context.include_recipe "shared::reboot_vm_#{nu.provider}"
          unless ssh_ready?(node[:ip], initialuser(node), key, 5, 150)
            Chef::Log.warn("Cannot ssh to #{node[:ip]} after a reboot.")
          end
        end
      end
    end
  end
end
