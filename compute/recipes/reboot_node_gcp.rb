dep = node[:workorder][:payLoad][:DependsOn]
mig_name = dep.detect { |d| d['ciClassName'] =~ /Computeset/ }['ciAttributes']['vmss_name']

ci = node[:workorder][:ci] || node[:workorder][:rfcCi]
mig = GCP::RegionalMIGMgr.new(node)
mig.mig_name = mig_name
vm_selflink = JSON.parse(ci[:ciAttributes][:metadata])['selfLink']

case node[:workorder][:actionName]
when 'reboot'
  mig.reboot_vm(vm_selflink)
when 'powercycle'
  mig.reset_vm(vm_selflink)
end
