#
# Cookbook Name:: compute
# Recipe:: base
#
# Copyright 2016, Walmart Stores, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

node.set['use_initial_user'] = true

include_recipe 'shared::set_provider_new'
include_recipe 'compute::ssh_port_wait'
include_recipe 'compute::ssh_cmd_for_remote'

key = node[:workorder][:payLoad][:SecuredBy][0][:ciAttributes][:private]

ruby_block 'wait for ssh' do
  block do
    node[:ostype] =~ /windows/ ? (wait_time = 20) : (wait_time = 4)

    # Wait until we can get a response from VM
    start_time = Time.now.to_i
    i = 1
    ssh_failed = true
    ssh_cmd = node[:ssh_interactive_cmd].gsub('IP',node[:ip])
    ssh_cmd = "#{ssh_cmd}hostname > /dev/null".gsub(
      'StrictHostKeyChecking=no','StrictHostKeyChecking=no -o ConnectTimeout=5'
    )
    Chef::Log.info("Started waiting for ssh response from #{node[:ip]}")
    Chef::Log.info("SSH command is: #{ssh_cmd}")

    # Try connecting for 300 seconds
    while Time.now.to_i - start_time < 300 do
      Chef::Log.info("Attempt:#{i} to get a valid ssh response from #{node[:ip]} ...")
      result = system(ssh_cmd)

      if result
        ssh_failed = false
        Chef::Log.info("Received a valid response from #{node[:ip]}! Moving on to a next step...")
        break
      end

      Chef::Log.info("Did not receive a valid response from #{node[:ip]}. Retrying...")
      sleep wait_time
      i += 1
    end

    if ssh_failed
      puts '***FATAL: SSH - we did not receive a valid response in 300 seconds'
      Chef::Log.warn('SSH - Deleting the VM')
      run_context.include_recipe 'compute::delete'
      exit_with_error('SSH - we did not receive a valid response in 300 seconds')
    end
  end
end if node[:workorder][:rfcCi][:rfcAction] !~ /update/

include_recipe 'compute::tty_off'

nu = ComputeComponent::NodeUtils.new(node)
ostype, os_version = nu.ostype.split('-')

ruby_block 'Enable RSA-SSH for Ubuntu 22+' do
  only_if do
    ostype =~ /ubuntu/ && Gem::Version.new(os_version) >= Gem::Version.new('22')
  end
  block do
    ssh_cmd = node[:ssh_interactive_cmd]
    sudo = ssh_cmd.include?('root@') ? '' : 'sudo '
    cmd = "#{sudo}sed -i '/PubkeyAcceptedKeyTypes/d' /etc/ssh/sshd_config"
    cmd += ";#{sudo}sed -i '\\$a\\PubkeyAcceptedKeyTypes\\ +ssh-rsa' /etc/ssh/sshd_config"
    cmd += ";#{sudo}service ssh restart"
    rc = execute_command("#{ssh_cmd}\"#{cmd}\"")
  end
end

ruby_block 'install base' do
  block do
    shell_timeout = 1800
    # var to hold all ssh commands to be executed
    # cmd format: short desc:::command:::command timeout, delimited by :::
    cmds = []
    fast_image = (node.has_key?('fast_image') && node['fast_image'])
    Chef::Log.info("Fast image #{fast_image ? '' : 'not'} detected")

    # install os package repos - repo_map keyed by os
    os_type = node[:ostype]
    cloud_name = node[:workorder][:cloud][:ciName]
    services = node['workorder']['services']
    compute_attr = services['compute'][cloud_name]['ciAttributes']

    unless fast_image
      repo_cmds = []
      if compute_attr.has_key?('repo_map') &&
         compute_attr[:repo_map].include?(os_type)
        repo_map = JSON.parse(compute_attr[:repo_map])
        repo_cmds = [repo_map[os_type]]
        Chef::Log.debug("repo_cmds: #{repo_cmds.inspect}")
      else
        Chef::Log.info('no key in repo_map for os: ' + os_type)
      end

      # add repo_list from os
      if node['repo_list'] && !node[:repo_list].nil? && node[:repo_list].include?('[')
        Chef::Log.info("adding compute-level repo_list: #{node[:repo_list]}")
        repo_cmds += JSON.parse(node[:repo_list])
      end
      cmd = repo_cmds.join('; ')
      cmds << "Repo setup:::#{cmd}:::#{shell_timeout}" unless repo_cmds.size.zero?
    end

    # Determine command prefix (sudo/powershell)
    cmd_prefix = if os_type =~ /windows/
                   'powershell.exe -NoProfile -ExecutionPolicy Bypass -File '
                 elsif node[:ssh_cmd].include?('root@')
                   ''
                 else
                   'sudo '
                 end

    # Determine install_base file
    base_file = "install_base.#{os_type =~ /windows/ ? 'ps1' : 'sh'}"
    dest_dir  = os_type =~ /windows/ ? '' : '~/'
    # Determine command arguments
    args = ''
    unless fast_image
      env_vars = JSON.parse(compute_attr['env_vars'])
      Chef::Log.info("env_vars: #{env_vars.inspect}")

      if os_type =~ /windows/
        env_vars.each_pair do |k, v|
          args += case k
                  when 'apiproxy' then "-proxy '#{v}' "
                  when 'rubygems' then "-gemRepo '#{v}' "
                  else ''
                  end
        end

        if services.has_key?('mirror') &&
           services['mirror'][cloud_name]['ciAttributes'].has_key?('mirrors')

          mirror_vars = JSON.parse(
            services['mirror'][cloud_name]['ciAttributes']['mirrors']
          )
          mirror_vars.each_pair do |k,v|
            args += case k
                    when 'chocopkg' then "-chocoPkg '#{v}' "
                    when 'chocorepo' then "-chocoRepo '#{v}' "
                    else ''
                    end
          end
        else
          Chef::Log.info('Compute does not have mirror service included')
        end
      else
        env_vars.each_pair { |k, v| args += "#{k}:#{v} " }
      end
    end

    # command to copy ind execute install file
    base_dir = File.join(File.expand_path('../../', __FILE__), '/files/default/')
    source_file = File.join(base_dir, base_file)
    cmds << "Copy #{source_file}:::scp #{source_file} ./:::120"
    cmd = "#{cmd_prefix} #{dest_dir}#{base_file} #{args}"
    cmds << "Install base:::#{cmd}:::#{shell_timeout}"

    # Command to create sudo file, copy and set perms for windows
    if os_type =~ /windows/
      sudo_file = File.join(base_dir, 'sudo')
      cmds << "Copy #{sudo_file}:::scp #{sudo_file} /usr/bin/.:::120"
      cmds << 'Set +x to sudo file:::chmod +x /usr/bin/sudo:::120'
    end

    # Commands to fetch cores and ram info
    cmd = 'grep processor /proc/cpuinfo | wc -l'
    cmds << "Get CORES info:::#{cmd}:::120"
    cmd = "free -m | awk '/Mem:/ { print $2 }'"
    cmds << "Get RAM info:::#{cmd}:::120"

    res = {}
    # execute all ssh commands in cmds array
    cmds.each_with_index do |item, idx|
      act_idx = idx + 1
      close_conn = act_idx == cmds.count
      tsk, cmd, timeout = item.split(':::')
      Chef::Log.info("#{tsk}, command: #{cmd}, timeout: #{timeout}, \
        close SSH conn?: #{close_conn}")
      res = ssh_exec(node[:ip], initialuser(node), key, cmd, timeout,
                     conn: res[:conn], close_conn: close_conn)
      res[:exit_code].zero? || exit_with_error("#{tsk} failed. #{res[:stderr]}")
      puts "***RESULT:cores=#{res[:stdout]}" if cmd =~ /cpuinfo/
      puts "***RESULT:ram=#{res[:stdout]}" if cmd =~ /free/
    end
  end
end

# Patch cloud-init to fix the issue with VMs w/o eph storage
ruby_block 'Patch cloud-init' do
  only_if do
    nu.provider == 'azure' && node[:vm_size] &&
    node[:vm_size] =~ /.*[ABDEFGL]\d+[^d]*_v[4,5]/i
  end
  block do
    Chef::Log.info("Patching cloud-init for Azure #{node[:vm_size]}")
    cmd = "sudo find /usr/lib -name DataSourceAzure.py |" \
      " xargs -r sudo sed -i 's/maxwait=120/maxwait=10/g'"
    res = ssh_exec(node[:ip], initialuser(node), key, cmd)
  end
end

include_recipe 'compute::unmount_eph_disk'

ruby_block 'register-rhn' do
  only_if { node[:ostype].downcase =~ /redhat/ }
  block do
    puts '***RESULT:is_rhn_registered=true' if register_rhn(node)
  end
end

include_recipe 'compute::service_registration'

if node[:workorder][:config] &&
   node[:workorder][:config][:sre_centrify] != 'true'
  include_recipe 'compute::add_sreadmin'
end
