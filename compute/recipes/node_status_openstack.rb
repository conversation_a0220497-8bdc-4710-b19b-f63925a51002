ci = node[:workorder][:ci]
ciAttrs = ci[:ciAttributes]
Chef::Log.debug("ci attrs: #{ci[:ciAttributes].inspect.gsub("\n"," ")}")
instance_id = ciAttrs[:instance_id]

server = node[:iaas_provider].servers.get instance_id

if server.nil?
  Chef::Log.error("cannot find server by instance_id: #{instance_id}")
  return false
end
if !server.flavor["id"].to_s.strip.empty?
  puts "***RESULT:vm_size="+server.flavor['id']
end

Chef::Log.info("server: "+server.inspect.gsub(/\n|\<|\>|\{|\}/,""))

puts "***RESULT:instance_state="+server&.state
puts "***RESULT:vm_state="+server&.os_ext_sts_vm_state