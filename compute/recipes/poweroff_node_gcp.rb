ci = node[:workorder][:ci] || node[:workorder][:rfcCi]
vm = ci[:ciAttributes][:instance_name]
mig = GCP::RegionalMIGMgr.new(node)

dep = node[:workorder][:payLoad][:DependsOn] # for removing from group
mig_name = dep.detect { |d| d['ciClassName'] =~ /Computeset/ }['ciAttributes']['vmss_name'] # for removing from group
mig.mig_name = mig_name # for removing from group
vm_selflink = JSON.parse(ci[:ciAttributes][:metadata])['selfLink'] # for removing from group

resp = mig.stop_vm(vm, vm_selflink)
