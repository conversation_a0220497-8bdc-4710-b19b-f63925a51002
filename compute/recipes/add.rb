#
# Cookbook Name:: compute
# Recipe:: add
#
# Copyright 2016, Walmart Stores, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

start_time = Time.now.to_i
include_recipe 'compute::node_lookup'

Chef::Log.info("compute::add -- name: #{node[:server_name]}")
puts "***RESULT:instance_name=#{node[:server_name]}"

Chef::Log.info("ostype: #{node[:ostype]} size: #{node[:size_id]} image: #{node[:image_id]}")

cloud_name = node[:workorder][:cloud][:ciName]
provider = node[:workorder][:services][:compute][cloud_name][:ciClassName].gsub('cloud.service.', '').downcase.split('.').last
node.set['cloud_provider'] = provider
Chef::Log.info("Cloud Provider: #{provider}")

# Handle Selective Deployment
rfcComputeset = (node[:workorder][:payLoad][:DependsOn].detect do |d|
  %w(Azurescaleset Computeset Bundle).include?(d[:ciClassName].split('.').last)
end)
if !rfcComputeset.nil? && rfcComputeset[:isActiveInRelease].to_s.downcase == 'false'
  vmss_attr = rfcComputeset[:ciAttributes]
  flex = vmss_attr[:orchestration] == 'flexible'
  vmss_id = vmss_attr[:vmss_id]
  Chef::Log.info('This is selective deployment, and computeset is not part of the release')

  # Currently we cannot support VMSS Uniform in selective deployments, as there's no way
  # for us to preserve the instance name, hence the computeset vmss_map attribute
  # will be out of sync

  if provider =~ /azure/ && vmss_id && !vmss_id.empty? && !flex
    exit_with_error('Selective deployments are currently not supported for VMSS Uniform')
  end

  lib_dir = File.absolute_path('../../computeset/libraries', __dir__)
  Dir.glob("#{lib_dir}/*.rb").each { |f| require f }

  #Overwrite node attributes
  rfcCompute = node[:workorder][:rfcCi]

  wo_orig = node[:workorder]
  wo_new = wo_orig.dup
  wo_new[:rfcCi] = rfcComputeset
  node.default[:IsComputeDpmt] = true
  node.default[:rfcCompute] = [rfcCompute]
  node.default[:selectiveDeployment] = true

  case provider
  when  /azure/
    node.default[:workorder] = wo_new
    include_recipe 'computeset::add_scaleset_azure'
    node.default[:workorder] = wo_orig
  when /gcp/
    node.set[:app_name] = 'computeset'
    node.set[:workorder] = wo_new
    node.set[:workorder][:rfcCi][:rfcAction] = 'update'
    include_recipe 'computeset::add_scaleset_gcp'
    node.set[:app_name] = 'compute'
    node.set[:workorder] = wo_orig
  else
    Chef::Log.info "Selective Deployment is not implemented for  #{provider}"
  end
end

# refactoring azure specific recipe to an azure folder to make it easier to manage all the files.
if provider =~ /azure/ && vmss_map(node) && use_vmss_template?(node)
  include_recipe 'azure::add_vmss_node'
elsif provider =~ /azure/
  include_recipe 'azure::add_node'
elsif provider =~ /vsphere/
  include_recipe 'vsphere::add_node'
else
  include_recipe "compute::add_node_#{provider}"
end

ruby_block 'Duration' do
  block do
    duration = Time.now.to_i - start_time
    Chef::Log.info("Took #{duration} sec to create or update ssh port open")
  end
end

# clear ptr on replace
if node[:workorder][:rfcCi][:ciState] == 'replace' &&
   node[:workorder][:rfcCi][:ciAttributes][:public_ip]
  provider_service = node[:workorder][:services][:dns][cloud_name][:ciClassName].split('.').last.downcase
  provider = 'fog'
  case provider_service
  when /infoblox/
    provider = 'infoblox'
    include_recipe 'fqdn::cleanup' # remove existing ip to dns that has been replaced.
  when /azuredns/
    provider = 'azuredns'
  when /designate/
    provider = 'designate'
  end
  include_recipe "fqdn::remove_ptr_#{provider}"
end

# sleeps based on average time for ssh to be ready even tho port is up
sleep_time = 10
if provider == 'ec2'
  case node[:ostype]
  when /centos|redhat/
    sleep_time = 30
  end
elsif provider == 'docker'
  sleep_time = 1
end

Chef::Log.info("Action is: #{node[:workorder][:rfcCi][:rfcAction]}")

ruby_block 'Wait for boot' do
  block do
    Chef::Log.info("waiting #{sleep_time}sec based on avg ready time for cloud provider and ostype")
    sleep sleep_time
  end
end

return if node[:pack] == 'custom-appliance'

include_recipe 'compute::base'
include_recipe 'compute::add_centrify'
