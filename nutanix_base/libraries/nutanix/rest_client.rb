require 'net/http'
require 'uri'
require 'json'
require 'openssl'
require_relative '../error_codes'
require_relative '../custom_exceptions'

class Nutanix
  class RestClient
    attr_accessor :username, :password, :domain, :base_url, :cookie

    def initialize(base_url, username, password, domain = nil)
      @base_url = base_url
      @username = username
      @password = password
      @domain = domain
      @cookie = nil
    end

    def authenticate
      payload = {
        username: @username,
        password: @password,
        domain: @domain
      }.to_json

      response = make_request(:post, 'auth/login', payload)
      @cookie = "cookie=#{response['cookie']}" if response['cookie']

      puts @cookie ? 'Authenticated successfully.' : "Authentication failed. Response: #{response}"
    end

    def get(resource)
      make_request(:get, resource)
    end

    def post(resource, payload = nil)
      make_request(:post, resource, payload)
    end

    def put(resource, payload = nil)
      make_request(:put, resource, payload)
    end

    def delete(resource, payload = nil)
      make_request(:delete, resource, payload)
    end

    def check_task_status(task_uuid)
      get("tasks/#{task_uuid}")
    end

    private

    def make_request(method, resource, payload = nil, nxrequestid = nil)
      nxrequestid = generate_uuid if resource.include?('v4')
      uri = URI.join(@base_url, resource)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = uri.scheme == 'https'
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE

      request = build_request(method, uri, payload)
      request['Content-Type'] = 'application/json'
      request['Accept'] = 'application/json'
      request['Cookie'] = @cookie if @cookie
      request['NTNX-Request-Id'] = nxrequestid if nxrequestid
      request.basic_auth(@username, @password) unless @cookie

      response = http.request(request)
      handle_response(response)
    end

    def build_request(method, uri, payload = nil)
      req = case method
            when :get
              Net::HTTP::Get.new(uri)
            when :post
              req = Net::HTTP::Post.new(uri)
              req.body = payload if payload
              req
            when :put
              req = Net::HTTP::Put.new(uri)
              req.body = payload if payload
              req
            when :delete
              req = Net::HTTP::Delete.new(uri)
              req.body = payload if payload
              req
            else
              raise ArgumentError, "Unsupported HTTP method: #{method}"
            end
    
      req['Content-Type'] = 'application/json' if payload && [:post, :put, :delete].include?(method)
      req
    end
    


    def handle_response(response)
      (response.body.nil? || response.body.empty?) ? {} : JSON.parse(response.body)
    end
  end
end
