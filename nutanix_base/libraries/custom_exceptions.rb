class CustomError < StandardError
  attr_reader :error_code, :details

  def initialize(error_code, details = nil)
    @error_code = error_code
    @details = details
    super(build_error_message)
  end

  private

  def build_error_message
    error_info = ErrorCodes.get_error(@error_code)
    message = "#{error_info[:message]} (#{error_info[:code]})"
    message += ": #{@details}" if @details
    message
  end
end
  
class VolumeCreationError < CustomError; end
class DiskAdditionError < CustomError; end
class VolumeAttachmentError < CustomError; end
class VolumeDetachmentError < CustomError; end
class VolumeDeletionError < CustomError; end
class RequestError < CustomError; end
class TaskStatusPollingError < CustomError; end
  