require 'securerandom'

def poll_task_status(task_uuid_endpoint, timeout: 300, interval: 10)
  start_time = Time.now
  puts("Waiting for #{interval} seconds for the task uuid #{task_uuid_endpoint} to be available")
  loop do
    begin
      sleep interval
      response = @nutanix_api.get(task_uuid_endpoint)

      if response.nil? || response.empty?
        puts("Received empty response while polling task status")
        next
      end

      task_status = response['status'] || response.dig('data', 'status')
      return :succeeded, response if task_status.casecmp('succeeded').zero?
      return :failed, response if task_status.casecmp('failed').zero?

      break if Time.now - start_time > timeout

      puts("Waiting for #{interval} seconds for the task uuid #{task_uuid_endpoint} to complete")
    rescue StandardError => e
      error_message = e.message
      puts("An error occurred while polling task status: #{error_message}")
      puts(e.backtrace.join("\n"))

      if error_message.include?('TASK_NOT_FOUND_ERROR')
        puts("Task not found, retrying after #{interval} seconds...")
        next
      else
        return :failed, {} # Return an empty hash if response_body is nil
      end
    end
  end

  :timeout
end

def generate_uuid
  SecureRandom.uuid
end

def convert_size_gb(disk_size_bytes)
  size_in_gb = disk_size_bytes.to_f / 1024 / 1024 / 1024
  size_in_gb.to_i 
end

def convert_size_bytes(size)
  size * (1024 ** 3)
end
