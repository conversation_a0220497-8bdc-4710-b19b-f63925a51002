require 'base64'
require 'json'
require 'timeout'
require 'socket'

require_relative 'vm_spec'
require_relative 'nutanix/rest_client'
require_relative 'utils'
require_relative 'volume_manager'

class Nutanix
  class VMManager
    VmCreationResult = Struct.new(:vm_uuid, :ip_addresses)

    def initialize(endpoint, username, password, version)
      @nutanix_api = Nutanix::RestClient.new(endpoint, username, password)
      @volume_manager = Nutanix::VolumeManager.new(endpoint, username, password, version)
    end

    def create_vm(server_name, cloudinit_content, disk_details, nic_details, enable_autocluster, cluster_uuid, cpu, memory)
      vm_exists, vm_uuid = vm_exists_by_name?(server_name)
      if vm_exists
        puts "VM with name #{server_name} already exists."
        ip_addresses = get_vm_ip_addresses(vm_uuid)
        return VmCreationResult.new(vm_uuid, ip_addresses)
      end

      encoded_data = Base64.encode64(cloudinit_content)

      resources = build_resources(encoded_data, disk_details, nic_details, cpu, memory)
      cluster_reference = build_cluster_reference(cluster_uuid, enable_autocluster)
      spec = Spec.new(name: server_name, resources: resources, cluster_reference: cluster_reference)
      vm_payload = VMPayload.new(metadata: Metadata.new, spec: spec).to_hash

      vm_obj = make_request_and_handle_errors(:post, 'api/nutanix/v3/vms', vm_payload.to_json, 'VM creation')
      vm_creation_result = handle_vm_response(vm_obj, 'VM creation')
      vm_creation_result
    end

    def delete_vm(vm_uuid, is_replace = false)
      unless vm_exists?(vm_uuid)
        puts "VM with UUID #{vm_uuid} does not exist."
        return
      end

      handle_volume_groups(vm_uuid, is_replace)

      response = make_request_and_handle_errors(:delete, "api/nutanix/v3/vms/#{vm_uuid}", nil, 'VM deletion')
      handle_vm_response(response, 'VM deletion', vm_uuid)
    end

    def handle_vm_response(response, action, vm_uuid = nil, operation_type = nil)
      task_uuid = extract_task_uuid(response, action)
      raise "task_uuid not found in the response" unless task_uuid
      status, response_body = poll_task_status("api/nutanix/v3/tasks/#{task_uuid}")
      handle_task_status(status, response_body, action, vm_uuid, operation_type)
    end

    def extract_task_uuid(response, action)
      if action == 'VM operation'
        api_response_list = response['api_response_list']
        if api_response_list && api_response_list[0]
          api_response = api_response_list[0]['api_response']
          return api_response['task_uuid'] if api_response
        end
      end
    
      status = response['status']
      if status && status['execution_context']
        return status['execution_context']['task_uuid']
      end

      nil
    end

    def handle_task_status(status, response_body, action, vm_uuid, operation_type)
      case status
      when :succeeded
        puts "#{action} succeeded."

        case action
        when 'VM creation'
          vm_uuid = handle_vm_creation_success(response_body)
          ip_addresses = reboot_vm_and_fetch_ip(vm_uuid)
          return VmCreationResult.new(vm_uuid, ip_addresses)
        when 'VM deletion'
          puts "VM with UUID #{vm_uuid} deleted successfully."
        when 'VM operation'
          puts "#{operation_type} operation on VM with UUID #{vm_uuid} is complete."
        end

      when :failed
        failure_reason = response_body.dig('error_detail') || 'Unknown failure reason'
        raise "#{action} failed: #{failure_reason}"

      when :timeout
        raise "#{action} timed out"
      end
    end

    # Checks if a TCP connection to the given IP and port can be established.
    def port_open?(ip, port, timeout = 3)
      begin
        Timeout.timeout(timeout) do
          s = TCPSocket.new(ip, port)
          s.close
          return true
        end
      rescue Errno::ECONNREFUSED, Errno::EHOSTUNREACH, Errno::ETIMEDOUT, Timeout::Error, SocketError
        return false
      end
    end

    # Retrieves all IP addresses assigned to the VM.
    def get_vm_ip_addresses(vm_uuid)
      vm = get_vm(vm_uuid)
      return [] unless vm

      ip_addresses = []
      nic_list = vm["status"]["resources"]["nic_list"] || []
      nic_list.each do |nic|
        if nic["ip_endpoint_list"]
          nic["ip_endpoint_list"].each do |endpoint|
            ip = endpoint["ip"]
            ip_addresses << ip if ip && !ip.empty?
          end
        end
      end
      ip_addresses
    end

    # Loops until an IP is assigned and port 22 is open.
    # If max retries is reached and IP(s) exist but port 22 is not open, returns the first IP.
    def wait_for_ip_assignment(vm_uuid, max_retries = 20, sleep_interval = 15)
      retries = 0

      loop do
        ip_addresses = get_vm_ip_addresses(vm_uuid)

        if ip_addresses.any?
          ip_addresses.each do |ip|
            if port_open?(ip, 22)
              puts "IP address #{ip} is assigned, and port 22 is open"
              return [ip]
            else
              puts "IP address #{ip} is assigned, but port 22 is not open yet."
            end
          end
        else
          puts "No IP address assigned yet for VM #{vm_uuid}."
        end

        retries += 1
        if retries >= max_retries
          if ip_addresses.any?
            puts "Max retries reached. Returning assigned IP(s): #{ip_addresses} despite port 22 not being open."
            return ip_addresses
          else
            exit_with_error "VM #{vm_uuid}: No IP address assigned after #{max_retries * sleep_interval} seconds."
          end
        end

        puts "Retrying (Attempt #{retries}/#{max_retries})..."
        sleep(sleep_interval)
      end
    end

    def handle_volume_groups(vm_uuid, is_replace)
      vm = get_vm(vm_uuid)
      disk_list = vm.dig("status", "resources", "disk_list") || []

      volume_groups = disk_list.select do |disk|
        disk['device_properties']['device_type'] == 'VOLUME_GROUP'
      end

      volume_groups.each do |vg|
        volume_group_id = vg['volume_group_reference']['uuid']
        puts "Detaching volume_group_id #{volume_group_id}"
        @volume_manager.detach_volume_group_from_vm(volume_group_id, vm_uuid)
        @volume_manager.delete_volume_group(volume_group_id) unless is_replace
      end
    end

    def update_vm(vm_uuid, update_payload)
      make_request_and_handle_errors(:put, "api/nutanix/v3/vms/#{vm_uuid}", update_payload, 'VM update')
      puts "VM with UUID #{vm_uuid} updated successfully."
    end

    def get_vm(vm_uuid)
      response = make_request_and_handle_errors(:get, "api/nutanix/v3/vms/#{vm_uuid}", nil, 'Get VM')
      return nil if response['code'] == 404
      response
    end

    def vm_exists?(vm_uuid)
      !!get_vm(vm_uuid)
    end

    def get_vm_by_name(vm_name)
      payload = {
        kind: "vm",
        filter: "vm_name==#{vm_name}"
      }
      response = @nutanix_api.post('api/nutanix/v3/vms/list', payload.to_json)
      vms = response['entities'] || []
      vms.find { |vm| vm['spec']['name'] == vm_name }
    rescue StandardError => e
      puts "An error occurred while searching for VM by name: #{e.message}"
      nil
    end

    def vm_exists_by_name?(vm_name)
      vm = get_vm_by_name(vm_name)
      if vm
        return true, vm['metadata']['uuid']
      else
        return false, nil
      end
    end

    def perform_vm_operation(vm_uuid, action)
      operation_type = case action
                       when 'reboot'
                         'acpi_reboot'
                       when 'powercycle'
                         'power_cycle'
                       else
                         raise ArgumentError, "Unknown action: #{action}"
                       end

      vm_operation = VMOperation.new(vm_id: vm_uuid, operation_type: operation_type).to_hash

      vm_operation_response = make_request_and_handle_errors(
        :post,
        'api/nutanix/v3/batch',
        vm_operation.to_json,
        'VM operation'
      )

      handle_vm_response(vm_operation_response, 'VM operation', vm_uuid, operation_type)
    end

    # Reboots the VM using the API, waits until the VM is ready, and then retrieves its IP address(es).
    def reboot_vm_and_fetch_ip(vm_uuid)
      puts "Initiating reboot for VM #{vm_uuid}"
      perform_vm_operation(vm_uuid, 'reboot')
      wait_for_vm_ready(vm_uuid)
      ip_addresses = wait_for_ip_assignment(vm_uuid)
      ip_addresses
    end

    # Polls the VM state until it is ready ("COMPLETE") or the max retries are reached.
    def wait_for_vm_ready(vm_uuid, max_retries = 20, sleep_interval = 15)
      retries = 0
      loop do
        vm = get_vm(vm_uuid)
        if vm && vm["status"] && vm["status"]["state"] == "COMPLETE"
          return true
        end
        retries += 1
        if retries >= max_retries
          raise "Timeout: VM #{vm_uuid} did not reach ready state after #{max_retries * sleep_interval} seconds."
        end
        puts "Waiting for VM #{vm_uuid} to be ready (attempt #{retries}/#{max_retries})..."
        sleep(sleep_interval)
      end
    end

    private

    def fetch_image_details(image_uuid)
      make_request_and_handle_errors(:get, "api/nutanix/v3/images/#{image_uuid}", nil, 'Get Image details')
    end

    def build_resources(encoded_data, disk_details, nic_details, cpu, memory)
      disk = Disk.new(enrich_disk_details(disk_details))
      boot_config = BootConfig.new
      nic = Nic.new(nic_details)
      guest_customization = encoded_data.empty? ? nil : GuestCustomization.new(user_data: encoded_data)

      Resources.new(
        disk_list: [disk],
        boot_config: boot_config,
        nic_list: [nic],
        guest_customization: guest_customization,
        num_sockets: cpu,
        memory_size_mib: memory
      )
    end

    def enrich_disk_details(disk_details)
      image = fetch_image_details(disk_details[:image_uuid])
      unless image && image["status"] && image["status"]["resources"]
        raise "Invalid image details for UUID: #{disk_details[:image_uuid]}"
      end
    
      # Duplicate disk_details to avoid side-effects and merge in additional details.
      enriched_details = disk_details.dup
      enriched_details[:disk_size_bytes] = image["status"]["resources"]["size_bytes"]
      enriched_details[:image_name] = image["status"]["name"]
    
      enriched_details
    end

    def build_cluster_reference(cluster_uuid, enable_autocluster)
      enable_autocluster ? nil : ClusterReference.new(uuid: cluster_uuid)
    end

    def handle_vm_creation_success(response_body)
      vm_uuid = extract_vm_uuid(response_body)
      vm_details = @nutanix_api.get("api/nutanix/v3/vms/#{vm_uuid}")
      display_vm_creation_details(vm_details)
      vm_uuid
    end

    def extract_vm_uuid(response_body)
      vm_entity = response_body['entity_reference_list'].find { |entity| entity['kind'] == 'vm' }
      vm_entity['uuid'] if vm_entity
    end

    def display_vm_creation_details(vm_details)
      cluster_reference = vm_details['status']['cluster_reference']
      cluster_uuid = cluster_reference['uuid']
      cluster_name = cluster_reference['name']
      puts "VM created in cluster #{cluster_name} with UUID #{cluster_uuid}"
    end

    def log_and_raise_error(action, error)
      puts "An error occurred during #{action}: #{error.message}"
      puts error.backtrace.join("\n")
      raise
    end

    def make_request_and_handle_errors(method, path, payload, action)
      case method
      when :get
        @nutanix_api.get(path)
      when :post
        @nutanix_api.post(path, payload)
      when :put
        @nutanix_api.put(path, payload)
      when :delete
        @nutanix_api.delete(path)
      else
        raise ArgumentError, "Unsupported HTTP method: #{method}"
      end
    rescue StandardError => e
      log_and_raise_error(action, e)
    end
  end
end
