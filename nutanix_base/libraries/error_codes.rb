module ErrorCodes
  unless const_defined?(:ERROR_CODES)
    ERROR_CODES = {
      general_error: { code: 1000, message: "An unknown error occurred." },
      volume_creation_failed: { code: 2000, message: "Volume group creation failed." },
      disk_addition_failed: { code: 2001, message: "Adding disk to volume group failed." },
      volume_attachment_failed: { code: 2002, message: "Attaching volume group to VM failed." },
      volume_detachment_failed: { code: 2003, message: "Detaching volume group from VM failed." },
      volume_disk_deletion_failed: { code: 2004, message: "Deleting volume disk failed." },
      volume_group_deletion_failed: { code: 2005, message: "Deleting volume group failed." },
      get_volume_group_by_name_failed: { code: 2006, message: "Get volume group by name failed." },
      get_vol_disk_attach_vol_group: { code: 2007, message: "Get volume disk attached to volume group failed." },
      get_volume_disk_failed: { code: 2008, message: "Get volume disk failed name failed." },
      get_vm_attachments_failed: { code: 2009, message: "Get VM attachments failed." },
      iscsi_client_attach_failed: { code: 2010, message: "Attaching iscsi client attach failed." },
      request_failed: { code: 3000, message: "HTTP request failed." },
      task_status_polling_failed: { code: 4000, message: "Polling task status failed." }
    }.freeze
  end

  def self.get_error(code_sym)
    ERROR_CODES[code_sym] || ERROR_CODES[:general_error]
  end
end
  