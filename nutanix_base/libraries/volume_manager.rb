require 'base64'
require 'json'

require_relative 'error_codes'
require_relative 'custom_exceptions'
require_relative 'nutanix/rest_client'
require_relative 'utils'
require_relative 'volume_config'

class Nutanix
  class VolumeManager
    def initialize(endpoint, username, password, api_version)
      @nutanix_api = Nutanix::RestClient.new(endpoint, username, password)
      @api_version = api_version.to_s  # ensure version is a string
    end

    def create_volume_group(name, cluster_reference, usage_type = "USER")
      volume_group = VolumeGroup.new(
        name: name,
        cluster_reference: cluster_reference,
        usage_type: usage_type
      ).to_json

      response = make_request_with_logging(:post, "/api/volumes/#{@api_version}/config/volume-groups", volume_group, :volume_creation_failed)
      handle_task(response, "Volume group creation") do |response_body|
        response_body["data"]["entitiesAffected"].find { |entity| entity["rel"] == "volumes:config:volume-group" }
      end
    end

    def add_disk_to_volume_group(volume_group_id, disk_size, disk_data_source_reference, description)
      volume_disk = VolumeDisk.new(
        disk_size_bytes: convert_size_bytes(disk_size),
        description: description,
        disk_data_source_reference: disk_data_source_reference
      ).to_json
      response = make_request_with_logging(:post, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_id}/disks", volume_disk, :disk_addition_failed)
      handle_task(response, "Disk addition")
    end

    def attach_volume_group_to_vm(volume_group_id, vm_ext_id)
      vm_attachment = VmAttachment.new(
        ext_id: vm_ext_id
      ).to_json

      response = make_request_with_logging(:post, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_id}/$actions/attach-vm", vm_attachment, :volume_attachment_failed)
      handle_task(response, "Volume attachment")
    end

    def detach_volume_group_from_vm(volume_group_id, vm_ext_id, index = 0)
      vm_detachment = VmDetachment.new(
        ext_id: vm_ext_id,
        index: index
      ).to_json

      response = make_request_with_logging(:post, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_id}/$actions/detach-vm", vm_detachment, :volume_detachment_failed)
      handle_task(response, "Volume detachment")
    end

    def delete_volume_disk(volume_group_ext_id, volume_disk_ext_id)
      response = make_request_with_logging(:delete, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_ext_id}/disks/#{volume_disk_ext_id}", nil, :volume_disk_deletion_failed)
      handle_task(response, "Volume disk deletion")
    end

    def delete_volume_group(volume_group_ext_id)
      response = make_request_with_logging(:delete, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_ext_id}", nil, :volume_group_deletion_failed)
      handle_task(response, "Volume group deletion")
    end

    def get_volume_group_by_name(volume_group_name)
      encoded_filter = URI.encode_www_form_component("name eq '#{volume_group_name}'")
      response = make_request_with_logging(:get, "/api/volumes/#{@api_version}/config/volume-groups?$filter=#{encoded_filter}", nil, :get_volume_group_by_name_failed)
      response && response['data'] ? response['data'].first : nil
    end

    def get_volume_group_id(volume_group_name)
      response = get_volume_group_by_name(volume_group_name)
      response.nil? ? nil : response['extId']
    end

    def get_volume_group_by_id(volume_group_ext_id)
      make_request_with_logging(:get, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_ext_id}", nil, :volume_group_deletion_failed)
    end

    def get_volume_group_disks(volume_group_ext_id)
      response = make_request_with_logging(:get, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_ext_id}/disks", nil, :get_volume_group_disks_failed)
      response && response['data'] ? response['data'] : nil
    end

    def get_volume_disk(volume_group_ext_id, volume_disk_id)
      response = make_request_with_logging(:get, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_ext_id}/disks/#{volume_disk_id}", nil, :get_volume_disk_failed)
      response && response['data'] ? response['data'] : nil
    end

    def get_vm_attachments(volume_group_ext_id)
      response = make_request_with_logging(:get, "/api/volumes/#{@api_version}/config/volume-groups/#{volume_group_ext_id}/vm-attachments", nil, :get_vm_attachments_failed)
      response && response['data'] ? response['data'] : nil
    end

    private

    def make_request_with_logging(method, path, payload, error_code_sym)
      payload.nil? ? @nutanix_api.send(method, path) : @nutanix_api.send(method, path, payload)
    rescue StandardError => e
      raise_custom_error(error_code_sym, e)
    end

    def raise_custom_error(error_code_sym, original_error)
      error = ErrorCodes.get_error(error_code_sym)
      raise CustomError.new(error_code_sym, original_error.message)
    end

    def handle_task(response, action)
      task_uuid = response['data']['extId']
      status, response_body = poll_task_status(task_uuid_endpoint(task_uuid))
      handle_task_status(status, action)
      yield(response_body) if block_given?
    end

    def handle_task_status(status, action)
      case status
      when :succeeded
        puts "#{action} succeeded"
      when :failed
        raise "#{action} failed"
      when :timeout
        raise "#{action} timed out"
      end
    end

    def task_uuid_endpoint(task_uuid)
      "api/prism/#{@api_version}/config/tasks/#{task_uuid}"
    end

  end
end
