class Nic
  attr_accessor :is_connected, :subnet_uuid, :subnet_name

  def initialize(*args, **kwargs)
    # If a single hash argument is passed (legacy style) with no separate keywords,
    # treat it as the options hash.
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @is_connected = kwargs.fetch(:is_connected, true)
    @subnet_uuid  = kwargs.fetch(:subnet_uuid)
    @subnet_name  = kwargs.fetch(:subnet_name)
  end

  def to_hash
    {
      is_connected: @is_connected,
      subnet_reference: {
        uuid: @subnet_uuid,
        name: @subnet_name,
        kind: "subnet"
      }
    }
  end
end
