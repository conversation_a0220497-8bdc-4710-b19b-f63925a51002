class Disk
  attr_accessor :device_type, :adapter_type, :device_index, :disk_size_bytes,
                :storage_container_uuid, :storage_container_name, :image_uuid, :image_name

  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @device_type = kwargs.fetch(:device_type, "DISK")
    @adapter_type = kwargs.fetch(:adapter_type, "SCSI")
    @device_index = kwargs.fetch(:device_index, 0)
    @disk_size_bytes = kwargs.fetch(:disk_size_bytes, 10632560640)
    @storage_container_uuid = kwargs[:storage_container_uuid]
    @storage_container_name = kwargs[:storage_container_name]
    @image_uuid = kwargs.fetch(:image_uuid)
    @image_name = kwargs.fetch(:image_name)
  end

  def to_hash
    disk_hash = {
      device_properties: {
        device_type: @device_type,
        disk_address: {
          adapter_type: @adapter_type,
          device_index: @device_index
        }
      },
      disk_size_bytes: @disk_size_bytes,
      data_source_reference: {
        kind: "image",
        uuid: @image_uuid,
        name: @image_name
      }
    }

    if @storage_container_uuid && @storage_container_name
      disk_hash[:storage_config] = {
        storage_container_reference: {
          kind: "storage_container",
          uuid: @storage_container_uuid,
          name: @storage_container_name
        }
      }
    end

    disk_hash
  end
end
