class VMPayload
  attr_accessor :metadata, :spec, :api_version

  def initialize(*args, **kwargs)
    # If a single hash is passed as a positional argument (legacy style), use it as kwargs.
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @metadata = kwargs.fetch(:metadata)
    @spec = kwargs.fetch(:spec)
    @api_version = kwargs.fetch(:api_version, "3.1.0")
  end

  def to_hash
    {
      metadata: @metadata.to_hash,
      spec: @spec.to_hash,
      api_version: @api_version
    }
  end
end
