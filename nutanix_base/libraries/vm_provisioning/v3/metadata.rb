class Metadata
  attr_accessor :categories_mapping, :kind, :use_categories_mapping

  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @categories_mapping = kwargs.fetch(:categories_mapping, {})
    @kind = kwargs.fetch(:kind, "vm")
    @use_categories_mapping = kwargs.fetch(:use_categories_mapping, true)
  end

  def to_hash
    {
      categories_mapping: @categories_mapping,
      kind: @kind,
      use_categories_mapping: @use_categories_mapping
    }
  end
end
