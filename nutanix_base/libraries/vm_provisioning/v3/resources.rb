class Resources
  attr_accessor :memory_overcommit_enabled, :num_sockets, :memory_size_mib, :power_state,
                :num_vcpus_per_socket, :hardware_clock_timezone, :disk_list, :gpu_list,
                :boot_config, :guest_customization, :nic_list

  def initialize(*args, **kwargs)
    # Support legacy calls by checking for a single hash argument.
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @memory_overcommit_enabled = kwargs.fetch(:memory_overcommit_enabled, false)
    @num_sockets               = kwargs.fetch(:num_sockets, 2)
    @memory_size_mib           = kwargs.fetch(:memory_size_mib, 4096)
    @power_state               = kwargs.fetch(:power_state, "ON")
    @num_vcpus_per_socket      = kwargs.fetch(:num_vcpus_per_socket, 1)
    @hardware_clock_timezone   = kwargs.fetch(:hardware_clock_timezone, "UTC")
    @disk_list                 = kwargs.fetch(:disk_list)    
    @boot_config               = kwargs.fetch(:boot_config)
    @nic_list                  = kwargs.fetch(:nic_list) 
    @guest_customization       = kwargs.fetch(:guest_customization, nil)
    @gpu_list                  = []
  end

  def to_hash
    resources_hash = {
      memory_overcommit_enabled: @memory_overcommit_enabled,
      num_sockets:               @num_sockets,
      memory_size_mib:           @memory_size_mib,
      power_state:               @power_state,
      num_vcpus_per_socket:      @num_vcpus_per_socket,
      hardware_clock_timezone:   @hardware_clock_timezone,
      disk_list:                 @disk_list.map(&:to_hash),
      gpu_list:                  @gpu_list,
      boot_config:               @boot_config.to_hash,
      nic_list:                  @nic_list.map(&:to_hash)
    }
    resources_hash[:guest_customization] = @guest_customization.to_hash if @guest_customization
    resources_hash
  end
end
