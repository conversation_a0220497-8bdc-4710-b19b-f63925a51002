class VMOperation
  attr_accessor :action_on_failure, :execution_order, :api_request_list, :api_version, :vm_id, :operation_type

  def initialize(*args, **kwargs)
    # If a single hash is passed as a positional argument (legacy style),
    # merge it into kwargs.
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @vm_id = kwargs.fetch(:vm_id)
    @operation_type = kwargs.fetch(:operation_type)
    @action_on_failure = kwargs.fetch(:action_on_failure, "CONTINUE")
    @execution_order = kwargs.fetch(:execution_order, "NON_SEQUENTIAL")
    @api_version = kwargs.fetch(:api_version, "3.0")
    @api_request_list = [
      {
        operation: "POST",
        path_and_params: "/api/nutanix/v3/vms/#{@vm_id}/#{@operation_type}",
        body: {}
      }
    ]
  end

  def to_hash
    {
      action_on_failure: @action_on_failure,
      execution_order: @execution_order,
      api_request_list: @api_request_list,
      api_version: @api_version
    }
  end
end
