class BootConfig
  attr_accessor :boot_type, :boot_device_order_list

  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @boot_type = kwargs.fetch(:boot_type, "LEGACY")
    @boot_device_order_list = kwargs.fetch(:boot_device_order_list, ['CDROM', 'DISK', 'NETWORK'])
  end

  def to_hash
    {
      boot_type: @boot_type,
      boot_device_order_list: @boot_device_order_list
    }
  end
end