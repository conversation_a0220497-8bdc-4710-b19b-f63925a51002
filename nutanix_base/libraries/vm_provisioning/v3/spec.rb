class Spec
  attr_accessor :name, :resources, :cluster_reference

  def initialize(*args, **kwargs)
    # If a single hash argument is provided (legacy style), use it as kwargs.
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @name = kwargs.fetch(:name)
    @resources = kwargs.fetch(:resources)
    @cluster_reference = kwargs.fetch(:cluster_reference, nil)
  end

  def to_hash
    spec_hash = {
      name: @name,
      resources: @resources.to_hash
    }
    spec_hash[:cluster_reference] = @cluster_reference.to_hash if @cluster_reference
    spec_hash
  end
end
