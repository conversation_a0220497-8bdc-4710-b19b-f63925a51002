class DeleteVolumeGroup
  attr_accessor :volume_group_ext_id, :volume_disk_ext_id

  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @volume_group_ext_id = kwargs.fetch(:volume_group_ext_id)
    @volume_disk_ext_id = kwargs.fetch(:volume_disk_ext_id)
  end

  def to_json(options = {})
    JSON.generate({
      volume_group_ext_id: @volume_group_ext_id,
      volume_disk_ext_id: @volume_disk_ext_id
    }, options)
  end
end