class IscsiFeatures
  attr_accessor :reserved, :object_type, :unknown_fields

  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    version = kwargs.fetch(:version, 'v4.r0.b1')
    @reserved      = { "$fv" => version }
    @object_type   = "volumes.v4.config.IscsiFeatures"
    @unknown_fields = {}
  end

  def to_hash
    {
      "$reserved" => @reserved,
      "$objectType" => @object_type,
      "$unknownFields" => @unknown_fields
    }
  end
end