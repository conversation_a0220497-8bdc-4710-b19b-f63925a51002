class VmAttachment
  attr_accessor :ext_id, :reserved, :object_type, :unknown_fields
  
  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @ext_id = kwargs.fetch(:ext_id)
    version = kwargs.fetch(:version, 'v4.r0.b1')
    @reserved      = { "$fv" => version }
    @object_type   = "volumes.v4.config.VmAttachment"
    @unknown_fields = {}
  end
  
  def to_json(options = {})
    JSON.generate({
      extId: @ext_id,
      "$reserved" => @reserved,
      "$objectType" => @object_type,
      "$unknownFields" => @unknown_fields
    }, options)
  end
end