class VolumeGroup
  attr_accessor :name, :sharing_status, :iscsi_features, :cluster_reference,
                :usage_type, :reserved, :object_type, :unknown_fields
  
  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @name             = kwargs.fetch(:name)
    @cluster_reference = kwargs.fetch(:cluster_reference)
    @usage_type       = kwargs.fetch(:usage_type)
    version           = kwargs.fetch(:version, 'v4.r0.b1')
    @sharing_status   = "SHARED"
    @iscsi_features   = IscsiFeatures.new(version: version)
    @reserved         = { "$fv" => version }
    @object_type      = "volumes.v4.config.VolumeGroup"
    @unknown_fields   = {}
  end
  
  def to_json(options = {})
    # If the cluster_reference responds to to_hash, use that.
    cluster_ref = @cluster_reference.respond_to?(:to_hash) ? @cluster_reference.to_hash : @cluster_reference

    JSON.generate({
      name: @name,
      sharingStatus: @sharing_status,
      iscsiFeatures: @iscsi_features.to_hash,
      clusterReference: cluster_ref,
      usageType: @usage_type,
      "$reserved" => @reserved,
      "$objectType" => @object_type,
      "$unknownFields" => @unknown_fields
    }, options)
  end
end