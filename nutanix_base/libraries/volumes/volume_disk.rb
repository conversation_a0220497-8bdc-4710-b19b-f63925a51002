class VolumeDisk
  attr_accessor :disk_size_bytes, :description, :disk_data_source_reference,
                :reserved, :object_type, :unknown_fields

  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @disk_size_bytes = kwargs.fetch(:disk_size_bytes)
    @description     = kwargs.fetch(:description)
    version          = kwargs.fetch(:version, 'v4.r0.b1')
    data_source_ref  = kwargs.fetch(:disk_data_source_reference)
    @disk_data_source_reference = EntityReference.new(ext_id: data_source_ref, version: version)
    @reserved      = { "$fv" => version }
    @object_type   = "volumes.v4.config.VolumeDisk"
    @unknown_fields = { "extId" => nil }
  end

  def to_json(options = {})
    JSON.generate({
      diskSizeBytes: @disk_size_bytes,
      description: @description,
      diskDataSourceReference: @disk_data_source_reference.to_hash,
      "$reserved" => @reserved,
      "$objectType" => @object_type,
      "$unknownFields" => @unknown_fields
    }, options)
  end
end