class EntityReference
  attr_accessor :ext_id, :entity_type, :reserved, :object_type, :unknown_fields

  def initialize(*args, **kwargs)
    if args.size == 1 && kwargs.empty? && args.first.is_a?(Hash)
      kwargs = args.first
    end

    @ext_id = kwargs.fetch(:ext_id)
    version = kwargs.fetch(:version, 'v4.r0.b1')
    @entity_type   = "STORAGE_CONTAINER"
    @reserved      = { "$fv" => version }
    @object_type   = "common.v1.config.EntityReference"
    @unknown_fields = {}
  end

  def to_hash
    {
      extId: @ext_id,
      entityType: @entity_type,
      "$reserved" => @reserved,
      "$objectType" => @object_type,
      "$unknownFields" => @unknown_fields
    }
  end
end