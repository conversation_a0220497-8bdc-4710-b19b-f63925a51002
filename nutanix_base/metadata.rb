name             'Nutanix_base'
maintainer       'OneOps'
maintainer_email '<EMAIL>'
license          'Apache License, Version 2.0'
description      'Provides basic Nutanix setup logic for OneOps.'
long_description IO.read(File.join(File.dirname(__FILE__), 'README.md'))
version          '0.1.0'
depends          'shared'

grouping 'default',
         :access => "global",
         :packages => [ 'base', 'mgmt.cloud.service', 'cloud.service' ],
         :namespace => true
